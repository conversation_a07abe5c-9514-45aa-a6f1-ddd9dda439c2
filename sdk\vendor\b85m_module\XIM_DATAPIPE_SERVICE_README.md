# XIM 数据管道服务 (XIM Data Pipe Service) 实现说明

## 概述

本项目已成功添加了自定义的XIM数据管道服务，提供双向数据通信功能。该服务使用自定义UUID，允许BLE客户端与设备进行数据交换。

## 功能特性

1. **自定义XIM数据管道服务** - 使用自定义UUID (0xF000)
2. **数据值特征** - UUID 0xF001，支持读取和通知
3. **命令特征** - UUID 0xF002，支持写入和无响应写入
4. **双向通信** - 客户端可发送命令，设备可发送数据通知
5. **20字节数据缓冲** - 每个特征支持最大20字节数据

## 服务结构

```
XIM Data Pipe Service (UUID: 0xF000)
├── Data Pipe Value Characteristic (UUID: 0xF001)
│   ├── Properties: Read, Notify
│   ├── Value: 20 bytes buffer
│   └── Client Characteristic Configuration Descriptor (UUID: 0x2902)
└── Data Pipe Command Characteristic (UUID: 0xF002)
    ├── Properties: Write, Write Without Response
    └── Value: 20 bytes buffer
```

## API 函数

### 数据值管理

```c
// 更新数据管道值
void ximDataPipe_updateValue(u8 *data, u8 len);

// 获取数据管道值
u8 ximDataPipe_getValue(u8 *data, u8 maxLen);

// 发送数据通知给连接的客户端
int ximDataPipe_sendNotification(u8 *data, u8 len);
```

### 命令处理

```c
// 获取最后接收到的命令数据
u8 ximDataPipe_getCommand(u8 *data, u8 maxLen);
```

## 使用场景

### 1. 客户端发送命令到设备

1. 客户端连接到设备
2. 发现XIM数据管道服务 (UUID: 0xF000)
3. 写入命令到命令特征 (UUID: 0xF002)
4. 设备接收命令并处理

### 2. 设备发送数据到客户端

1. 设备更新数据值
2. 发送通知给已连接的客户端
3. 客户端接收数据通知

## 使用示例

### 发送数据通知

```c
// 准备要发送的数据
u8 sensor_data[] = {0x01, 0x02, 0x03, 0x04, 0x05};

// 发送通知（如果客户端已启用通知）
if(device_in_connection_state) {
    ximDataPipe_sendNotification(sensor_data, sizeof(sensor_data));
}
```

### 处理接收到的命令

```c
// 在命令回调函数中处理
int ximDataPipeCommand_onReceiveData(rf_packet_att_write_t *para)
{
    u8 len = para->l2capLen - 3;
    if(len > 0 && len <= sizeof(dataPipeCommand))
    {
        // 复制接收到的命令数据
        memcpy(dataPipeCommand, &(para->value), len);
        
        // 处理命令
        switch(dataPipeCommand[0]) {
            case 0x01: // 示例命令1
                // 处理逻辑
                break;
            case 0x02: // 示例命令2
                // 处理逻辑
                break;
            default:
                // 未知命令，回显数据
                ximDataPipe_sendNotification(dataPipeCommand, len);
                break;
        }
    }
    return 0;
}
```

### 读取命令数据

```c
// 在应用程序中读取最后接收到的命令
u8 command_buffer[20];
u8 cmd_len = ximDataPipe_getCommand(command_buffer, sizeof(command_buffer));

if(cmd_len > 0) {
    // 处理命令数据
    process_command(command_buffer, cmd_len);
}
```

## 数据格式

### 命令格式建议

```c
typedef struct {
    u8 cmd_id;      // 命令ID
    u8 length;      // 数据长度
    u8 data[18];    // 命令数据 (最大18字节)
} ximDataPipe_command_t;
```

### 数据格式建议

```c
typedef struct {
    u8 data_type;   // 数据类型
    u8 length;      // 数据长度
    u8 payload[18]; // 有效载荷 (最大18字节)
} ximDataPipe_data_t;
```

## 属性表更新

XIM数据管道服务已添加到GATT属性表中：
- 句柄范围：0x0013 - 0x0018
- 服务句柄：0x0013
- 数据值特征句柄：0x0015
- 数据值CCC句柄：0x0016
- 命令特征句柄：0x0018

## 初始化

服务在 `user_init_train()` 函数中初始化：

```c
// 初始化XIM数据管道，设置默认数据
u8 init_data[] = "XIM_READY";
ximDataPipe_updateValue(init_data, sizeof(init_data)-1);
```

## 注意事项

1. **数据长度限制**：每个特征最大支持20字节数据
2. **通知启用**：客户端需要启用通知才能接收数据
3. **连接状态**：只有在设备连接状态下才能发送通知
4. **内存管理**：数据缓冲区使用静态分配，注意数据覆盖
5. **并发访问**：在多线程环境中注意数据同步

## 扩展功能

### 1. 命令队列

可以实现命令队列来处理多个命令：

```c
#define CMD_QUEUE_SIZE 10
static u8 cmd_queue[CMD_QUEUE_SIZE][20];
static u8 cmd_queue_head = 0;
static u8 cmd_queue_tail = 0;
```

### 2. 数据分片

对于大于20字节的数据，可以实现分片传输：

```c
typedef struct {
    u8 fragment_id;
    u8 total_fragments;
    u8 data[18];
} fragment_t;
```

### 3. 错误处理

添加错误码和状态反馈：

```c
#define XIM_STATUS_OK           0x00
#define XIM_STATUS_ERROR        0x01
#define XIM_STATUS_INVALID_CMD  0x02
#define XIM_STATUS_BUSY         0x03
```

## 测试建议

1. 使用BLE调试工具连接设备
2. 发现XIM数据管道服务
3. 启用数据值特征的通知
4. 向命令特征写入测试数据
5. 验证设备响应和数据通知
6. 测试不同长度的数据传输
7. 验证错误处理机制
