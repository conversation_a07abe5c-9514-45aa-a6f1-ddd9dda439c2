{"folders": [{"path": "."}], "settings": {"terminal.integrated.shellIntegration.enabled": false, "clangd.arguments": ["--header-insertion=never"], "files.autoGuessEncoding": true, "C_Cpp.default.configurationProvider": "cl.eide", "C_Cpp.errorSquiggles": "disabled", "files.associations": {".eideignore": "ignore", "*.a51": "a51", "*.h": "c", "*.c": "c", "*.hxx": "cpp", "*.hpp": "cpp", "*.c++": "cpp", "*.cpp": "cpp", "*.cxx": "cpp", "*.cc": "cpp"}, "[yaml]": {"editor.insertSpaces": true, "editor.tabSize": 4, "editor.autoIndent": "advanced"}, "EIDE.Toolchain.AnyGcc.InstallDirectory": "C:\\App\\TelinkIoTStudio\\opt\\tc32", "EIDE.Toolchain.AnyGcc.ToolPrefix": "tc32-elf-"}, "extensions": {"recommendations": ["cl.eide", "keroc.hex-fmt", "xiaoyongdong.srecord", "hars.cppsnippets", "zixuanwang.linkerscript", "redhat.vscode-yaml", "IBM.output-colorizer", "cschlosser.doxdocgen", "ms-vscode.vscode-serial-monitor"]}}