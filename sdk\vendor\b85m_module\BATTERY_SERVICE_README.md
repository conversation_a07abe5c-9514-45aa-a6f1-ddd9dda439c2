# 电池服务 (Battery Service) 实现说明

## 概述

本项目已成功添加了标准的BLE电池服务 (Battery Service)，允许BLE客户端读取和监控设备的电池电量。

## 功能特性

1. **标准BLE电池服务** - 使用标准UUID (0x180F)
2. **电池电量特征** - 使用标准UUID (0x2A19)
3. **读取支持** - 客户端可以读取当前电池电量
4. **通知支持** - 电池电量变化时自动通知客户端
5. **电压转换** - 自动将电池电压转换为百分比

## 服务结构

```
Battery Service (UUID: 0x180F)
└── Battery Level Characteristic (UUID: 0x2A19)
    ├── Properties: Read, Notify
    ├── Value: 1 byte (0-100 表示电量百分比)
    └── Client Characteristic Configuration Descriptor (UUID: 0x2902)
```

## API 函数

### 电池电量管理

```c
// 更新电池电量值 (0-100)
void battery_level_update(u8 level);

// 获取当前电池电量值
u8 battery_level_get(void);

// 发送电池电量通知给连接的客户端
int battery_level_notify(void);
```

### 电池电压处理

```c
// 将电池电压转换为百分比
u8 battery_voltage_to_percentage(u16 voltage_mv);

// 获取当前电池电压 (毫伏)
u16 battery_get_voltage_mv(void);
```

## 配置说明

### 电池检测启用时 (BATT_CHECK_ENABLE = 1)

- 系统会自动读取实际的电池电压
- 电压范围：2000mV (0%) 到 3300mV (100%)
- 每500ms更新一次电池电量
- 电量变化时自动发送BLE通知

### 电池检测禁用时 (BATT_CHECK_ENABLE = 0)

- 使用演示模式，默认电量为85%
- 每5秒更新一次（可根据需要修改）
- 可以通过API手动设置电量值

## 使用示例

### 客户端连接和读取电池电量

1. 连接到设备
2. 发现服务 (UUID: 0x180F)
3. 读取电池电量特征 (UUID: 0x2A19)
4. 启用通知以接收电量变化

### 手动更新电池电量

```c
// 设置电池电量为75%
battery_level_update(75);

// 如果设备已连接，发送通知
if(device_in_connection_state) {
    battery_level_notify();
}
```

## 电压到百分比转换

当前使用线性转换：
- 2000mV = 0%
- 3300mV = 100%
- 公式：percentage = (voltage - 2000) * 100 / 1300

可以根据实际电池特性修改 `battery_voltage_to_percentage()` 函数来实现更精确的转换曲线。

## 属性表更新

电池服务已添加到GATT属性表中：
- 句柄范围：0x000F - 0x0012
- 其他服务的句柄相应后移

## 注意事项

1. 电池电量值范围为0-100，超出范围会被自动限制
2. 只有在客户端启用通知时才会发送电量变化通知
3. 电量变化时才发送通知，避免不必要的通信
4. 需要设备处于连接状态才能发送通知

## 测试建议

1. 使用BLE调试工具连接设备
2. 读取电池服务和特征
3. 启用通知
4. 观察电量变化通知
5. 验证电量值的准确性
