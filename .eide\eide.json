{"name": "tlsr827x", "type": "ANY-GCC", "dependenceList": [], "srcDirs": ["sdk"], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": []}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "244faf1a56740847046c3ad651c23f7f"}, "targets": {"Debug": {"excludeList": ["sdk/drivers/8258", "sdk/vendor/b85m_master_kma_dongle", "sdk/vendor/b85m_hci", "sdk/vendor/b85m_feature_test", "sdk/vendor/b85m_driver_test", "sdk/vendor/b85m_ble_sample", "sdk/vendor/b85m_ble_remote", "sdk/boot/8251", "sdk/boot/8253", "sdk/boot/8258", "sdk/boot/8271", "sdk/boot/8278/cstartup_8278_RET_16K.S"], "toolchain": "ANY_GCC", "compileConfig": {"linkerScriptPath": "sdk\\boot.link", "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["sdk", "sdk/common", "sdk/drivers/8278"], "libList": ["sdk/proj_lib"], "defineList": ["CHIP_TYPE=CHIP_TYPE_827x", "__PROJECT_8278_MODULE__"]}, "builderOptions": {"ANY_GCC": {"version": 2, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "ELF Size", "disable": false, "abortAfterFailed": true, "command": "tc32-elf-size -t ${OutDirBase}/${ProjectName}.elf"}, {"name": "Strip Binary", "disable": false, "abortAfterFailed": true, "command": "tc32-elf-objcopy -v -O binary ${OutDirBase}/${ProjectName}.elf ${OutDirBase}/${ProjectName}.bin"}, {"name": "Check FW", "disable": false, "abortAfterFailed": true, "command": "sdk\\tl_check_fw2.exe ${OutDirBase}/${ProjectName}.bin"}], "global": {"output-debug-info": "enable"}, "c/cpp-compiler": {"language-c": "gnu99", "language-cpp": "gnu++11", "one-elf-section-per-function": true, "one-elf-section-per-data": true, "signed-char": true, "C_FLAGS": "-Wall -fpack-struct -fshort-enums -finline-small-functions -fshort-wchar -fms-extensions", "optimization": "level-2", "warnings": "unspecified"}, "asm-compiler": {"defines": ["MCU_STARTUP_8278_RET_32K"]}, "linker": {"output-format": "elf", "$disableOutputTask": true, "remove-unused-input-sections": true, "linker-type": "ld", "LIB_FLAGS": "-llt_827x -llt_general_stack"}}}}}, "version": "3.6"}