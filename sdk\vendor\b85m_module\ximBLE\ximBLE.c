#include "ximBLE.h"

#define 	MY_APP_ADV_CHANNEL					BLT_ENABLE_ADV_ALL
#define 	MY_ADV_INTERVAL_MIN					ADV_INTERVAL_30MS
#define 	MY_ADV_INTERVAL_MAX					ADV_INTERVAL_35MS

// int app_host_event_callback (u32 h, u8 *para, int n)
// {

// 	u8 event = h & 0xFF;

// 	switch(event)
// 	{
// 		case GAP_EVT_SMP_PAIRING_BEGIN:
// 		{
// 			gap_smp_pairingBeginEvt_t *pEvt = (gap_smp_pairingBeginEvt_t *)para;
// 			tlkapi_send_string_data(APP_SMP_LOG_EN, "[APP][SMP] paring begin:", pEvt, sizeof(gap_smp_pairingBeginEvt_t));
// 		}
// 		break;

// 		case GAP_EVT_SMP_PAIRING_SUCCESS:
// 		{
// 			gap_smp_pairingSuccessEvt_t *pEvt = (gap_smp_pairingSuccessEvt_t *)para;
// 			tlkapi_send_string_data(APP_SMP_LOG_EN, "[APP][SMP] paring success:", pEvt, sizeof(gap_smp_pairingSuccessEvt_t));
// 		}
// 		break;

// 		case GAP_EVT_SMP_PAIRING_FAIL:
// 		{
// 			gap_smp_pairingFailEvt_t *pEvt = (gap_smp_pairingFailEvt_t *)para;
// 			tlkapi_send_string_data(APP_SMP_LOG_EN, "[APP][SMP] paring fail:", pEvt, sizeof(gap_smp_pairingFailEvt_t));
// 		}
// 		break;

// 		case GAP_EVT_SMP_CONN_ENCRYPTION_DONE:
// 		{
// 			//gap_smp_connEncDoneEvt_t *pEvt = (gap_smp_connEncDoneEvt_t *)para;
// 		}
// 		break;

// 		case GAP_EVT_SMP_SECURITY_PROCESS_DONE:
// 		{
// 			//gap_smp_securityProcessDoneEvt_t *pEvt = (gap_smp_securityProcessDoneEvt_t *)para;
// 		}
// 		break;


// 		case GAP_EVT_SMP_TK_DISPLAY:
// 		{
// 			//u32 pinCode = MAKE_U32(para[3], para[2], para[1], para[0]);
// 		}
// 		break;

// 		case GAP_EVT_SMP_TK_REQUEST_PASSKEY:
// 		{
// 			//for this event, no data, "para" is NULL
// 		}
// 		break;

// 		case GAP_EVT_SMP_TK_REQUEST_OOB:
// 		{
// 			//for this event, no data, "para" is NULL
// 		}
// 		break;

// 		case GAP_EVT_SMP_TK_NUMERIC_COMPARE:
// 		{
// 			//u32 pinCode = MAKE_U32(para[3], para[2], para[1], para[0]);
// 		}
// 		break;

// 		case GAP_EVT_ATT_EXCHANGE_MTU:
// 		{
// 			gap_gatt_mtuSizeExchangeEvt_t *pEvt = (gap_gatt_mtuSizeExchangeEvt_t *)para;
// 			tlkapi_send_string_data(APP_HOST_EVENT_LOG_EN, "[APP][MTU] mtu exchange", pEvt, sizeof(gap_gatt_mtuSizeExchangeEvt_t));
// 		}
// 		break;

// 		case GAP_EVT_GATT_HANDLE_VALUE_CONFIRM:
// 		{
// 			//for this event, no data, "para" is NULL
// 		}
// 		break;


// 		default:
// 		break;
// 	}

// 	return 0;
// }



void ximBLE_MacInit()
{
    blc_initMacAddress(flash_sector_mac_address, ximBleConfig.mac_pubilc, ximBleConfig.mac_random_static);
}

// void ximBLE_init()
// {
//     ximBLE_MacInit();

//     blc_ll_initBasicMCU();                      //mandatory
// 	blc_ll_initStandby_module(ximBleConfig.mac_pubilc);		//mandatory
// 	blc_ll_initAdvertising_module(ximBleConfig.mac_pubilc); 	//legacy advertising module: mandatory for BLE slave
// 	blc_ll_initConnection_module();				//connection module  mandatory for BLE slave/master
// 	blc_ll_initSlaveRole_module();				//slave module: 	 mandatory for BLE slave,

//     blc_gap_peripheral_init();
//     blc_l2cap_register_handler (blc_l2cap_packet_receive);

//     xim_att_init();
//     // blc_att_setRxMtuSize(MTU_SIZE_SETTING);

//     blc_smp_setSecurityLevel(No_Security);
//     // blc_gap_registerHostEventHandler(app_host_event_callback);
//     blc_gap_setEventMask( GAP_EVT_MASK_SMP_PAIRING_BEGIN 			|  \
// 						  GAP_EVT_MASK_SMP_PAIRING_SUCCESS   		|  \
// 						  GAP_EVT_MASK_SMP_PAIRING_FAIL				|  \
// 						  GAP_EVT_MASK_ATT_EXCHANGE_MTU);

//     #if (BLE_OTA_SERVER_ENABLE)
// 		////////////////// OTA relative ////////////////////////
// 		#if (UART_PRINT_DEBUG_ENABLE)
// 			blc_debug_addStackLog(STK_LOG_OTA_FLOW);
// 		#endif
// 		blc_ota_initOtaServer_module();

// 		//blc_ota_setOtaProcessTimeout(30);   //OTA process timeout:  30 seconds
// 		//blc_ota_setOtaDataPacketTimeout(4);	//OTA data packet timeout:  4 seconds
// 		// blc_ota_registerOtaStartCmdCb(app_enter_ota_mode);
// 		// blc_ota_registerOtaResultIndicationCb(app_ota_end_result);
// 	#endif

//     u8 adv_param_status = BLE_SUCCESS;
//     adv_param_status = bls_ll_setAdvParam(  MY_ADV_INTERVAL_MIN, MY_ADV_INTERVAL_MAX,
// 											 ADV_TYPE_CONNECTABLE_UNDIRECTED, OWN_ADDRESS_PUBLIC,
// 											 0,  NULL,
// 											 MY_APP_ADV_CHANNEL,
// 											 ADV_FP_NONE);
//     if(adv_param_status)
//         tlkapi_printf(APP_LOG_EN, "[APP][INI] ADV parameters error 0x%x!!!\n", adv_param_status);

//     advertising_init(0xFF00, ximBleConfig.ctrolType);

//     rf_set_power_level_index (RF_POWER_P3dBm);
    
//     ximBLE_blsInit();
    
// }