/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : config.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2025/09/12    yuanpai       N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#pragma once

#include "stack/ble/ble.h"
#include <stdint.h>

typedef struct{
    uint8_t mac_pubilc[6];
    uint8_t mac_random_static[6];

    uint8_t ctrolType[2];

    uint8_t deviceName[16];
    uint8_t deviceOtaName[16];
    // uint8_t deviceCtrolType[2];
    uint8_t manufacturerName[20];
    uint8_t modelNumber[20];
    uint8_t serialNumber[20];
    uint8_t hardwareRevision[20];
    uint8_t firmwareRevision[20];
    uint8_t softwareRevision[20];

    att_readwrite_callback_t ximDataPipeCommandCallback;
    att_readwrite_callback_t ximConfigPipeCommandCallback;
    att_readwrite_callback_t ximCalibPipeCommandCallback;
    att_readwrite_callback_t ximCalibCheckPipeCommandCallback;
    att_readwrite_callback_t tlkSppClient2ServerCallback;

    uint16_t ximDataPipeValueHandle;
    uint16_t ximDataPipeCommandHandle;
    uint16_t ximConfigPipeValueHandle;
    uint16_t ximConfigPipeCommandHandle;
    uint16_t ximCalibPipeValueHandle;
    uint16_t ximCalibPipeCommandHandle;
    uint16_t ximCalibCheckPipeValueHandle;
    uint16_t ximCalibCheckPipeCommandHandle;
    uint16_t tlkSppDataClient2ServerHandle;
    uint16_t tlkSppDataServer2ClientHandle;

}ximBleConfig_t;


typedef struct{
    uint8_t manufacturerName[20];
    uint8_t modelNumber[20];
    uint8_t serialNumber[20];
    uint8_t hardwareRevision[20];
    uint8_t firmwareRevision[20];
    uint8_t softwareRevision[20];
}ximBleStorage_t;

extern ximBleConfig_t ximBleConfig;

void ximBLE_Config_init(void);
