#include "app_train.h"
#include "app.h"
#include "app_att.h"
#include "app_config.h"
#include "app_xim.h"
#include "drivers.h"
#include "stack/ble/ble.h"
#include "tl_common.h"
#include "version.h"

// 已支持关键字：后台->蓝牙:
//              mcu->蓝牙：

#define MAX_RETRY_TIMES 5

_attribute_data_retention_ u32 device_in_connection_state = 0;
_attribute_data_retention_ u8 close_laser_flag = 0;
_attribute_data_retention_ u8 node_status = 0b11;
_attribute_data_retention_ u8 node_battery = 0b11;
_attribute_data_retention_ u8 bullet_type = 0x0d;        // 弹药类型;
_attribute_data_retention_ u8 shot_cnt = 0;
_attribute_data_retention_ u8 laser_status = 0;           // 激光器打开：1, 关闭：0
_attribute_data_retention_ u8 laser_track = 0x55;         // 激光轨迹开：0xaa, 关闭：0x55
_attribute_data_retention_ u8 group_id = 0;               // 红蓝队
_attribute_data_retention_ u8 clothes_id[3] = {};         // 衣服id
_attribute_data_retention_ u8 retry_buffer[2][40];        // 启用+重试次数+空2位+uart头+数据

static u8 trans_data_uart[32] = { 0x81 };
static u8 trans_data_ble[32] = { 0x81 };
// static const u8 ble_cmd_table[] = { 0xdb, 4, 0xde, 15, 0xd4, 5, 0xd6, 8, 0xd0, 3 };
static u8 data_20_frame[16] = { 0x81, 0xc2, 0x01, 0x02, 0x00, 0x14, 0x00, 0x5a };

u8 send_cmd_preid();
u8 send_cmd_query();
u8 send_cmd_unlock_reset();
u8 send_cmd_close_laser();
u8 gun_read_id();
u8 get_version();
u8 get_ble_mac();
u8 check_gun_id(u8 *p);
u8 bls_uart_handler(u8 *rx_data, u8 len);
u8 ble_send_data(u8 *data, int len);
u8 Checksum(u8 *arr_ptr, u8 len);
u8 timing_and_info(u8 *rx_data);
u8 change_ble_mac(u8 *data);
int retry_send();
int send_20_Frame();
char ble_data_analysis(u8 *data, int len);

extern u8 mac_public[6];
extern my_fifo_t spp_rx_fifo;
extern my_fifo_t spp_tx_fifo;
extern void app_suspend_exit();

/**
 * @brief      callback function of LinkLayer Event
 * @param[in]  h     - LinkLayer Event type
 * @param[in]  param - data pointer of event
 * @param[in]  n     - data length of event
 * @return     none
 */
int controller_event_handler(u32 h, u8 *para, int n) {

    if ((h & HCI_FLAG_EVENT_TLK_MODULE) != 0)        // module event
    {

        u8 event = (u8)(h & 0xff);

        switch (event)
        {
        case BLT_EV_FLAG_SCAN_RSP:
            break;

        case BLT_EV_FLAG_CONNECT: {
            bls_l2cap_requestConnParamUpdate(CONN_INTERVAL_10MS, CONN_INTERVAL_15MS, 99, CONN_TIMEOUT_4S);
            device_in_connection_state = 1;
            gun_read_id();
            GPIO_LED_HIGH
        }
        break;

        case BLT_EV_FLAG_TERMINATE: {
            device_in_connection_state = 0;
            GPIO_LED_LOW
        }
        break;

        case BLT_EV_FLAG_CONN_PARA_REQ: {
            // Slave received Master's LL_Connect_Update_Req pkt.
            rf_packet_ll_updateConnPara_t p;
            memcpy((u8 *)&p.winSize, para, 11);

            // printf("Receive Master's LL_Connect_Update_Req pkt.\n");
            // printf("Connection interval:%dus.\n", p.interval * 1250);
        }
        break;

        case BLT_EV_FLAG_SUSPEND_EXIT:
            app_suspend_exit();
            break;

        case BLT_EV_FLAG_GPIO_EARLY_WAKEUP:
        case BLT_EV_FLAG_CHN_MAP_REQ:
        case BLT_EV_FLAG_CHN_MAP_UPDATE:
        case BLT_EV_FLAG_CONN_PARA_UPDATE:
        case BLT_EV_FLAG_ADV_DURATION_TIMEOUT:
        case BLT_EV_FLAG_SUSPEND_ENTER:
        default:
            break;
        }
    }

    return 0;
}

// int app_host_event_callback(u32 h, u8 *para, int n) {
//     u8 event = (u8)(h & 0xff);

//     switch (event)
//     {
//     case GAP_EVT_SMP_TK_DISPALY:
//         return 999999;
//     default:
//         break;
//     }
// }

/**
 * @brief		处理uart接收数据.
 * @param[in]	none
 * @return      0 is ok
 */
int rx_from_uart_cb(void) {
    if (my_fifo_get(&spp_rx_fifo) == 0)
        return 0;

    u8 *p = my_fifo_get(&spp_rx_fifo);
    u32 rx_len = p[0];        // usually <= 255 so 1 byte should be sufficient

    if (rx_len > 0 && rx_len <= 46)
    {
        // 删除有ack的重试缓存
        if (p[5] == retry_buffer[0][9])
            retry_buffer[0][0] = 0;
        else if (p[5] == retry_buffer[1][9])
            retry_buffer[1][0] = 0;
        // GPIO_LED_LOW
        bls_uart_handler(&p[4], rx_len);
        my_fifo_pop(&spp_rx_fifo);
    }

    return 0;
}

// 向ble发送通知
u8 ble_send_data(u8 *data, int len) {
    return bls_att_pushNotifyData(SPP_SERVER_TO_CLIENT_DP_H, data, len);
}

/**
 * @brief		向uart缓存中写入数据.
 * @param[in]   pEvent - event data
 * @return      0 is ok
 */
char train_send_data(u8 *data, int n) {
    u8 *p = my_fifo_wptr(&spp_tx_fifo);
    if (!p || (n + 4) >= spp_tx_fifo.size)
        return -1;

    *p++ = n;
    *p++ = n >> 8;

    memcpy(p, data, n);
    p += n;

    my_fifo_next(&spp_tx_fifo);

    return 0;
}

uart_data_t T_txdata_buf;

/**
 * @brief		uart发送回调
 * @param[in]	none
 * @return      0 is ok
 */
int tx_to_uart_cb(void) {
    u8 *p = my_fifo_get(&spp_tx_fifo);
    if (p && !uart_tx_is_busy())
    {
        memcpy(&T_txdata_buf.data, p + 2, p[0] + p[1] * 256);
        T_txdata_buf.len = p[0] + p[1] * 256;

        if (uart_dma_send((u8 *)(&T_txdata_buf)))
        {
            if (!(retry_buffer[0][0] || retry_buffer[1][0]))
            {
                blt_soft_timer_add(&retry_send, 150000);
            }

            u8 *ptr;
            if (retry_buffer[0][0] == 0 || retry_buffer[0][9] == T_txdata_buf.data[1])
                ptr = retry_buffer[0];
            else
                ptr = retry_buffer[1];

            ptr[0] = 1;
            ptr[1] = 0;
            ptr[2] = 0;
            ptr[3] = 0;
            memcpy(&ptr[4], &T_txdata_buf, T_txdata_buf.len + 4);

            my_fifo_pop(&spp_tx_fifo);
        }
    }
    return 0;
}

void user_init_train() {
    send_cmd_preid();
    blt_soft_timer_add(&send_20_Frame, 10000000);

    // Initialize battery level to a reasonable default value
    battery_level_update(90);  // Start with 90% battery level

    // Initialize XIM data pipe service
    app_xim_init();
}

// mcu->uart数据处理
u8 bls_uart_handler(u8 *rx_data, u8 len) {
    // 蓝牙转发mcu数据
    //  ble_send_data(rx_data, len);
    //  return 0;

    if (rx_data[0] != 0x81)
        return 0;
    // d4指令mcu将两次uart合并发过来，校验和特殊处理。
    if ((rx_data[1] != 0xd4) && (Checksum(rx_data, len - 1) != rx_data[len - 1]))
        return 0;

    switch (rx_data[1])
    {
    // 预置id
    case 0xaf:
        // todo：判断是否一致？
        if ((rx_data[2] != mac_public[3]) || (rx_data[3] != mac_public[2]) || (rx_data[4] != mac_public[1]))
        {
            send_cmd_preid();
        }

        break;
    case 0xe3:
        // todo:sd计数
        // trans_data_ble[0] = 0x81;
        // trans_data_ble[1] = 0xe3;
        // trans_data_ble[2] = mac_public[1];
        // trans_data_ble[3] = mac_public[2];
        // trans_data_ble[4] = mac_public[3];
        // trans_data_ble[5] = mac_public[4];
        // trans_data_ble[6] = rx_data[2];
        // trans_data_ble[7] = rx_data[3];
        // trans_data_ble[8] = Checksum(trans_data_ble, 8);
        // ble_send_data(trans_data_ble, 9);
        trans_data_ble[0] = 0x81;
        trans_data_ble[1] = 0xe3;
        trans_data_ble[2] = mac_public[1];
        trans_data_ble[3] = mac_public[2];
        trans_data_ble[4] = mac_public[3];
        trans_data_ble[5] = rx_data[2];
        trans_data_ble[6] = rx_data[3];
        trans_data_ble[7] = Checksum(trans_data_ble, 7);
        ble_send_data(trans_data_ble, 8);
        break;

    case 0xdb:
        shot_cnt = rx_data[2];
        node_status = rx_data[3] >> 6;
        node_battery = (rx_data[3] >> 4) & 0b11;
        trans_data_ble[0] = 0x81;
        trans_data_ble[1] = 0xdb;
        trans_data_ble[2] = mac_public[1];
        trans_data_ble[3] = mac_public[2];
        trans_data_ble[4] = mac_public[3];
        trans_data_ble[5] = mac_public[4];
        trans_data_ble[6] = shot_cnt;
        trans_data_ble[7] = (rx_data[3] & 0xf0) + bullet_type;
        trans_data_ble[8] = Checksum(trans_data_ble, 8);
        ble_send_data(trans_data_ble, 9);
        break;

    case 0xdd:
        // if ((rx_data[2] & 0x10) == 0x00 && (rx_data[2] & 0x01) == 0x00)        //激光清零成功,打开激光器成功
        if (rx_data[2] == 0x00)        // 激光清零成功,打开激光器成功
        {
            shot_cnt = 0;
            laser_status = 1;
            trans_data_ble[0] = 0x81;
            trans_data_ble[1] = 0xdd;
            trans_data_ble[2] = mac_public[0];
            trans_data_ble[3] = Checksum(trans_data_ble, 3);
            ble_send_data(trans_data_ble, 4);
        }
        else
        {
            send_cmd_unlock_reset();
        }

        break;

    case 0xd4:
        // if (rx_data[0] + rx_data[1] + rx_data[2] != rx_data[3])
        //     return 0;

        if ((rx_data[2] & 0x01) == 0x0f)        // 关闭激光器成功
            laser_status = 0;
        else if ((rx_data[2] & 0x01) == 0x00)        // 关闭激光器失败
        {                                            /* code */
        }
        trans_data_ble[0] = 0x81;
        trans_data_ble[1] = 0xd4;
        trans_data_ble[2] = mac_public[1];
        trans_data_ble[3] = mac_public[2];
        trans_data_ble[4] = mac_public[3];
        trans_data_ble[5] = mac_public[4];
        trans_data_ble[6] = rx_data[2];
        trans_data_ble[7] = Checksum(trans_data_ble, 7);
        ble_send_data(trans_data_ble, 8);
        // rx_data[0]=len;
        // ble_send_data(rx_data, len);
        break;

    case 0xd6:
        laser_track = rx_data[5];
        // 激光轨迹,透传.
        ble_send_data(rx_data, len);
        break;

    case 0xc1:
    
        ble_send_data(rx_data, len);
        break;
    
    // c2关掉透传
    case 0xc2:
    default:
        break;
    }

    return 1;
}

// ble数据处理
char ble_data_handle(u8 *data, int len) {
    // 有时便携式把两个指令一起发过来
    // u8 ptr = 0;
    // while (len > 0)
    // {
    //     if (data[ptr] != 0x81)
    //         return 0;

    //     u8 cmd_l = 0;
    //     for (u8 i = 0; i < sizeof(ble_cmd_table); i += 2)
    //     {
    //         if (ble_cmd_table[i] == data[ptr + 1])
    //         {
    //             cmd_l = ble_cmd_table[i + 1];
    //             break;
    //         }
    //     }

    //     if (!cmd_l)
    //         return 0;

    //     ble_data_analysis(&data[ptr], cmd_l);
    //     len -= cmd_l;
    //     ptr += cmd_l;
    // }

    // 蓝牙数据打印
    // uart_data_t T_rxdata_buf;
    // T_rxdata_buf.data[0] = len;
    // memcpy(&T_rxdata_buf.data[1], data, len);
    // T_rxdata_buf.len = len + 1;
    // uart_dma_send((u8 *)(&T_rxdata_buf));
    // if (data[1] == 0xd0)
    // { gun_read_id(); }
    // return 0;

    return ble_data_analysis(data, len);
}

// ble数据解析
char ble_data_analysis(u8 *data, int len) {
    if (data[0] != 0x81)
        return 0;
    if (Checksum(data, len - 1) != data[len - 1])
        return 0;

    switch (data[1])
    {
    // 同步指令
    case 0xdb:
        // if ((data[2] != mac_public[0]) && (data[2] != 0xff))
        //     return 0;

        send_cmd_query();
        break;
    // 授时授信
    case 0xde:
        timing_and_info(data);
        break;
    // 关闭激光
    case 0xd4:
        if (check_gun_id(&data[2]))
            send_cmd_close_laser();

        break;
    // 读取Q号
    case 0xd0:
        gun_read_id();
        break;
    // 激光解锁
    case 0xdd:
        send_cmd_unlock_reset();
        break;
    // 一些透传
    case 0xd6:
    case 0xc1:
    case 0xc2:
        train_send_data(data, len);
        break;

    // 调试指令
    case 0x71:
        get_version();
        break;
    case 0x72:
        get_ble_mac();
        break;
    case 0x73:
        change_ble_mac(data);
        break;
    default:
        break;
    }

    return 1;
}

////////////////////////////////////// Communication Protocol /////////////////////////////////
// 预置ID号授信指令
u8 send_cmd_preid() {
    trans_data_uart[0] = 0x81;
    trans_data_uart[1] = 0xaf;
    trans_data_uart[2] = mac_public[3];
    trans_data_uart[3] = mac_public[2];
    trans_data_uart[4] = mac_public[1];
    trans_data_uart[5] = Checksum(trans_data_uart, 5);
    train_send_data(trans_data_uart, 6);

    return 0;
}

// 上位机查询
u8 send_cmd_query() {
    trans_data_uart[0] = 0x81;
    trans_data_uart[1] = 0xdb;
    trans_data_uart[2] = 0x5c;
    train_send_data(trans_data_uart, 3);

    return 1;
}

// 解锁清零
u8 send_cmd_unlock_reset() {
    trans_data_uart[0] = 0x81;
    trans_data_uart[1] = 0xdd;
    trans_data_uart[2] = 0x5e;
    train_send_data(trans_data_uart, 3);

    return 1;
}

// 关闭激光
u8 send_cmd_close_laser() {
    trans_data_uart[0] = 0x81;
    trans_data_uart[1] = 0xd4;
    trans_data_uart[2] = 0x0f;
    trans_data_uart[3] = Checksum(trans_data_uart, 3);
    train_send_data(trans_data_uart, 4);

    return 1;
}

// 授时授信
u8 timing_and_info(u8 *rx_data) {
    // unsigned char *gps_time = get_gps_time();
    // unsigned char i = 8;
    // while (i--)
    //     gps_time[i] = rx_data[i + 2];

    clothes_id[0] = rx_data[10];
    clothes_id[1] = rx_data[11];
    clothes_id[2] = rx_data[12];
    group_id = rx_data[13];

    trans_data_ble[0] = 0x81;
    trans_data_ble[1] = 0xde;
    trans_data_ble[2] = mac_public[1];
    trans_data_ble[3] = mac_public[2];
    trans_data_ble[4] = mac_public[3];
    trans_data_ble[5] = mac_public[4];
    trans_data_ble[6] = rx_data[4];
    trans_data_ble[7] = Checksum(trans_data_ble, 7);
    ble_send_data(trans_data_ble, 8);
    // todo 低功耗处理

    return 1;
}

// id号读取
u8 gun_read_id() {
    trans_data_ble[0] = 0x81;
    trans_data_ble[1] = 0xd0;
    trans_data_ble[2] = mac_public[1];
    trans_data_ble[3] = mac_public[2];
    trans_data_ble[4] = mac_public[3];
    trans_data_ble[5] = mac_public[4];
    trans_data_ble[6] = 0;
    trans_data_ble[7] = 0;
    trans_data_ble[8] = Checksum(trans_data_ble, 8);
    ble_send_data(trans_data_ble, 9);

    return 1;
}

u8 get_version() {
    trans_data_ble[0] = 0x81;
    trans_data_ble[1] = 0x71;
    trans_data_ble[2] = FIRMWARE_MICRO_VERSION;
    trans_data_ble[3] = FIRMWARE_MINOR_VERSION;
    trans_data_ble[4] = FIRMWARE_MAJOR_VERSION;
    trans_data_ble[5] = PROTOCOL_MINOR_VERSION;
    trans_data_ble[6] = PROTOCOL_MAJOR_VERSION;
    trans_data_ble[7] = Checksum(trans_data_ble, 7);
    ble_send_data(trans_data_ble, 8);

    return 1;
}

u8 get_ble_mac() {
    trans_data_ble[0] = 0x81;
    trans_data_ble[1] = 0x72;
    flash_read_page(flash_sector_mac_address, 6, &trans_data_ble[2]);
    trans_data_ble[8] = Checksum(trans_data_ble, 8);
    ble_send_data(trans_data_ble, 9);
    return 1;
}

// 修改蓝牙地址
u8 change_ble_mac(u8 *data) {
    flash_erase_sector(flash_sector_mac_address);
    WaitUs(20);
    flash_write_page(flash_sector_mac_address, 6, data + 2);
    WaitUs(20);
    trans_data_ble[0] = 0x81;
    trans_data_ble[1] = 0x73;
    flash_read_page(flash_sector_mac_address, 6, &trans_data_ble[2]);
    WaitUs(40);
    trans_data_ble[8] = Checksum(trans_data_ble, 8);
    ble_send_data(trans_data_ble, 9);
    return 1;
}

// 关闭轨迹
// int send_cmd_close_tracker() {
//     trans_data_uart[0] = 0x81;
//     trans_data_uart[1] = 0xd6;
//     trans_data_uart[2] = mac_public[1];
//     trans_data_uart[3] = mac_public[2];
//     trans_data_uart[4] = mac_public[3];
//     trans_data_uart[5] = 0x55;
//     trans_data_uart[6] = 0;
//     trans_data_uart[7] = Checksum(trans_data_uart, 7);
//     train_send_data(trans_data_uart, 8);

//     close_tracker_cnt--;
//     return close_tracker_cnt >= 0 ? 0 : -1;
// }

int retry_send() {
    if ((!retry_buffer[0][0]) && (!retry_buffer[1][0]))
        return -1;

    u8 *ptr;
    if (retry_buffer[0][0])
        ptr = retry_buffer[0];
    else if (retry_buffer[1][0])
        ptr = retry_buffer[1];

    if (uart_dma_send((u8 *)(&ptr[4])))
    {
        if (++ptr[1] >= MAX_RETRY_TIMES)
        {
            ptr[0] = 0;
            // 重试次数超
        }
    }

    return 0;
}


int send_20_Frame() {
    
    train_send_data(data_20_frame, 8);
    return 0;
}

u8 check_gun_id(u8 *p) {
    if ((*p == mac_public[1]) && (*(p + 1) == mac_public[2]) && (*(p + 2) == mac_public[3]) &&
        (*(p + 3) == mac_public[4]))
        return 1;
    else
        return 0;
}

// 校验和
u8 Checksum(u8 *arr_ptr, u8 len) {
    u8 sum = 0;
    while (len--)
        sum += arr_ptr[len];

    return sum;
}
