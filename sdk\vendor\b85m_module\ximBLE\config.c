/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : config.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2025/09/12    yuanpai       N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#include "config.h"
// #include "Productinfo/Productinfo.h"

ximBleConfig_t ximBleConfig;



void ximBLE_Config_init(void)
{
    // DEBUG_PRINTF("Compile date and time is %s",&SoftInfo_t.CompileTime[0]);

    memset(&ximBleConfig, 0, sizeof(ximBleConfig));

    strcpy((char *)ximBleConfig.deviceName, "XimDemo");
    strcpy((char *)ximBleConfig.deviceOtaName, "XimDemo_OTA");

    strcpy((char *)ximBleConfig.manufacturerName, "Ximmerse");
    strcpy((char *)ximBleConfig.modelNumber, "XimDemo");
    strcpy((char *)ximBleConfig.serialNumber, "1234567890");
    strcpy((char *)ximBleConfig.hardwareRevision, "0000");
    strcpy((char *)ximBleConfig.firmwareRevision, "0000");

    // memcpy((char *)ximBleConfig.softwareRevision, SoftInfo_t.ProdSoftVer, PROD_SOFT_LEN);
    strcpy((char *)ximBleConfig.softwareRevision, "1.0");
    
    ximBleConfig.ctrolType[0] = 0x35;
    ximBleConfig.ctrolType[1] = 0x63;

    ximBleConfig.ximDataPipeCommandCallback = NULL;
    ximBleConfig.ximConfigPipeCommandCallback = NULL;
    ximBleConfig.ximCalibPipeCommandCallback = NULL;
    ximBleConfig.ximCalibCheckPipeCommandCallback = NULL;
    ximBleConfig.tlkSppClient2ServerCallback = NULL;

}


