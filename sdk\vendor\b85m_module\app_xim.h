/********************************************************************************************************
 * @file	app_xim.h
 *
 * @brief	This is the header file for XIM Data Pipe Service
 *
 * <AUTHOR> GROUP
 * @date	2024
 *
 * @par     Copyright (c) 2024, Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *          All rights reserved.
 *
 *******************************************************************************************************/

#ifndef APP_XIM_H_
#define APP_XIM_H_

#include "tl_common.h"
#include "stack/ble/ble.h"

/**
 * @brief      Initialize XIM data pipe service
 * @param[in]  none
 * @return     none
 */
void app_xim_init(void);

/**
 * @brief      Update XIM data pipe value
 * @param[in]  data - pointer to data buffer
 * @param[in]  len - data length (max 20 bytes)
 * @return     none
 */
void ximDataPipe_updateValue(u8 *data, u8 len);

/**
 * @brief      Get XIM data pipe value
 * @param[in]  data - pointer to buffer to store data
 * @param[in]  maxLen - maximum buffer size
 * @return     actual data length
 */
u8 ximDataPipe_getValue(u8 *data, u8 maxLen);

/**
 * @brief      Send XIM data pipe notification to connected client
 * @param[in]  data - pointer to data buffer
 * @param[in]  len - data length (max 20 bytes)
 * @return     0 if success, non-zero if failed
 */
int ximDataPipe_sendNotification(u8 *data, u8 len);

/**
 * @brief      Get last received XIM data pipe command
 * @param[in]  data - pointer to buffer to store command data
 * @param[in]  maxLen - maximum buffer size
 * @return     actual command data length
 */
u8 ximDataPipe_getCommand(u8 *data, u8 maxLen);

/**
 * @brief      XIM data pipe command write callback
 * @param[in]  para - rf_packet_att_write_t
 * @return     0
 */
int ximDataPipeCommand_onReceiveData(rf_packet_att_write_t *para);

/**
 * @brief      Check if XIM data pipe notifications are enabled
 * @param[in]  none
 * @return     1 if enabled, 0 if disabled
 */
u8 ximDataPipe_isNotificationEnabled(void);

/**
 * @brief      Set XIM data pipe notification state (called when CCC is written)
 * @param[in]  enabled - 1 to enable, 0 to disable
 * @return     none
 */
void ximDataPipe_setNotificationEnabled(u8 enabled);



/**
 * @brief      Update XIM data buffer with sensor/status information
 * @param[in]  none
 * @return     none
 * @note       This is a placeholder function for you to implement your data update logic
 */
void ximDataPipe_updateBuffer(void);

/**
 * @brief      Test function to verify XIM data pipe functionality
 * @param[in]  none
 * @return     none
 */
void ximDataPipe_test(void);

#endif /* APP_XIM_H_ */