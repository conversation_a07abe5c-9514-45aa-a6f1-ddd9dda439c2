/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : adv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2025/09/12    yuanpai       N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#include "ximBLE.h"
#include "stack/ble/ble.h"

typedef struct {
    uint8_t advData[31];
    uint8_t advDataLen;
    uint8_t scanRspData[31];
    uint8_t scanRspDataLen;
}ximBLE_AdvData_t;

static uint8_t ximBLE_AdvData_init(ximBLE_AdvData_t *advData) //, uint16_t uudis, uint8_t *CtrolType
{
    advData->advDataLen = 0;
    advData->scanRspDataLen = 0;

    return 0;
}

static uint8_t ximBLE_AdvData_addFlags(ximBLE_AdvData_t *advData, bool onScanRsp, uint8_t flags)
{
    uint8_t *advd = onScanRsp? advData->scanRspData : advData->advData;
    uint8_t *advl = onScanRsp? &advData->scanRspDataLen : &advData->advDataLen;

    advd[(*advl)++] = 0x02;
    advd[(*advl)++] = DT_FLAGS;
    advd[(*advl)++] = flags;

    return 0;
}

static uint8_t ximBLE_AdvData_addName(ximBLE_AdvData_t *advData, bool onScanRsp, uint8_t *name, uint8_t len)
{
    uint8_t *advd = onScanRsp? advData->scanRspData : advData->advData;
    uint8_t *advl = onScanRsp? &advData->scanRspDataLen : &advData->advDataLen;

    advd[(*advl)++] = len + 1;
    advd[(*advl)++] = DT_COMPLETE_LOCAL_NAME;
    for (uint8_t i = 0; i < len; i++)
    {
        advd[(*advl)++] = name[i];
    }

    return 0;

}

static uint8_t ximBLE_AdvData_addUUID16(ximBLE_AdvData_t *advData, bool onScanRsp, uint16_t uuid)
{
    uint8_t *advd = onScanRsp? advData->scanRspData : advData->advData;
    uint8_t *advl = onScanRsp? &advData->scanRspDataLen : &advData->advDataLen;

    advd[(*advl)++] = 0x03;
    advd[(*advl)++] = DT_COMPLETE_LIST_16BIT_SERVICE_UUID;
    advd[(*advl)++] = (uint8_t)(uuid & 0xFF);
    advd[(*advl)++] = (uint8_t)(uuid >> 8);

    return 0;
}

static uint8_t ximBLE_AdvData_addManufacturerData(ximBLE_AdvData_t *advData, bool onScanRsp, uint16_t companyID, uint8_t *data, uint8_t len)
{
    uint8_t *advd = onScanRsp? advData->scanRspData : advData->advData;
    uint8_t *advl = onScanRsp? &advData->scanRspDataLen : &advData->advDataLen;

    advd[(*advl)++] = len + 3;
    advd[(*advl)++] = 0xFF;//DT_MANUFACTURER_SPECIFIC_DATA;
    advd[(*advl)++] = (uint8_t)(companyID & 0xFF);
    advd[(*advl)++] = (uint8_t)(companyID >> 8);
    for (uint8_t i = 0; i < len; i++)
    {
        advd[(*advl)++] = data[i];
    }

    return 0;
}

static uint8_t ximBLE_AdvData_addAppearance(ximBLE_AdvData_t *advData, bool onScanRsp, uint16_t appearance)
{
    uint8_t *advd = onScanRsp? advData->scanRspData : advData->advData;
    uint8_t *advl = onScanRsp? &advData->scanRspDataLen : &advData->advDataLen;

    advd[(*advl)++] = 0x03;
    advd[(*advl)++] = DT_APPEARANCE;
    advd[(*advl)++] = (uint8_t)(appearance & 0xFF);
    advd[(*advl)++] = (uint8_t)(appearance >> 8);

    return 0;
}


void advertising_updata(uint16_t uudis, uint8_t *CtrolType)
{
    uint8_t xim_manuf_data[] = 
    {
        0x00, //scene ID
        'X', 'i','m','m','s','1','2',
    };
    xim_manuf_data[6] = CtrolType[0];
    xim_manuf_data[7] = CtrolType[1];

    ximBLE_AdvData_t advData;
    ximBLE_AdvData_init(&advData);
    ximBLE_AdvData_addFlags(&advData, false, 0x06);
    ximBLE_AdvData_addName(&advData, false, ximBleConfig.deviceName, strlen(ximBleConfig.deviceName));
    ximBLE_AdvData_addUUID16(&advData, false, uudis);
    ximBLE_AdvData_addManufacturerData(&advData, false, 0xF000,xim_manuf_data, sizeof(xim_manuf_data));
    ximBLE_AdvData_addAppearance(&advData, true, 0x0102);

    bls_ll_setAdvData(advData.advData, advData.advDataLen);
    bls_ll_setScanRspData(advData.scanRspData, advData.scanRspDataLen);
}

void advertising_init(uint16_t uudis, uint8_t *CtrolType)
{
    advertising_updata(uudis, CtrolType);
    bls_ll_setAdvEnable(BLC_ADV_ENABLE);
}

