/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : att.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2025/09/12    yuanpai       N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/
#pragma once


enum
{
    XIM_ATTRIBUTES_MAX = 100,
};

typedef struct __attribute__((packed))
{
    uint8_t gatt_char_prop;
    uint16_t gatt_char_handle;
    uint16_t gatt_char_uuid;
} gatt_char_t;

typedef struct 
{
  /** Minimum value for the connection event (interval. 0x0006 - 0x0C80 * 1.25 ms) */
  uint16_t intervalMin;
  /** Maximum value for the connection event (interval. 0x0006 - 0x0C80 * 1.25 ms) */
  uint16_t intervalMax;
  /** Number of LL latency connection events (0x0000 - 0x03e8) */
  uint16_t latency;
  /** Connection Timeout (0x000A - 0x0C80 * 10 ms) */
  uint16_t timeout;
} gap_periConnectParams_t;

void xim_att_init();


