// #include "bls.h"

// _attribute_data_retention_	u32	latest_user_event_tick;
// _attribute_data_retention_	u32 advertise_begin_tick;
// _attribute_data_retention_	u32	latest_user_event_tick;
// _attribute_data_retention_	int device_in_connection_state;

// /**
//  * @brief      callback function of LinkLayer Event "BLT_EV_FLAG_CONNECT"
//  * @param[in]  e - LinkLayer Event type
//  * @param[in]  p - data pointer of event
//  * @param[in]  n - data length of event
//  * @return     none
//  */
// void	task_connect (u8 e, u8 *p, int n)
// {
// 	(void)e;(void)p;(void)n;
// 	tlk_contr_evt_connect_t *pConnEvt = (tlk_contr_evt_connect_t *)p;
// 	tlkapi_send_string_data(APP_CONTR_EVENT_LOG_EN, "[APP][EVT] connect, intA & advA:", pConnEvt->initA, 12);
// //	bls_l2cap_requestConnParamUpdate (CONN_INTERVAL_10MS, CONN_INTERVAL_10MS, 19, CONN_TIMEOUT_4S);  // 200mS
// 	bls_l2cap_requestConnParamUpdate (CONN_INTERVAL_10MS, CONN_INTERVAL_10MS, 99, CONN_TIMEOUT_4S);  // 1 S
// //	bls_l2cap_requestConnParamUpdate (CONN_INTERVAL_10MS, CONN_INTERVAL_10MS, 149, CONN_TIMEOUT_8S);  // 1.5 S
// //	bls_l2cap_requestConnParamUpdate (CONN_INTERVAL_10MS, CONN_INTERVAL_10MS, 199, CONN_TIMEOUT_8S);  // 2 S
// //	bls_l2cap_requestConnParamUpdate (CONN_INTERVAL_10MS, CONN_INTERVAL_10MS, 249, CONN_TIMEOUT_8S);  // 2.5 S
// //	bls_l2cap_requestConnParamUpdate (CONN_INTERVAL_10MS, CONN_INTERVAL_10MS, 299, CONN_TIMEOUT_8S);  // 3 S

// 	latest_user_event_tick = clock_time();

// 	device_in_connection_state = 1;//


// #if (UI_LED_ENABLE && !TEST_CONN_CURRENT_ENABLE)
// 	gpio_write(GPIO_LED_RED, LED_ON_LEVEL);  //red led on
// #endif
// }



// /**
//  * @brief      callback function of LinkLayer Event "BLT_EV_FLAG_TERMINATE"
//  * @param[in]  e - LinkLayer Event type
//  * @param[in]  p - data pointer of event
//  * @param[in]  n - data length of event
//  * @return     none
//  */
// void 	task_terminate(u8 e, u8 *p, int n) //*p is terminate reason
// {
// 	(void)e;(void)n;


// 	device_in_connection_state = 0;


// 	tlk_contr_evt_terminate_t *pEvt = (tlk_contr_evt_terminate_t *)p;
// 	if(pEvt->terminate_reason == HCI_ERR_CONN_TIMEOUT){

// 	}
// 	else if(pEvt->terminate_reason == HCI_ERR_REMOTE_USER_TERM_CONN){

// 	}
// 	else if(pEvt->terminate_reason == HCI_ERR_CONN_TERM_MIC_FAILURE){

// 	}
// 	else{

// 	}

// 	tlkapi_printf(APP_CONTR_EVENT_LOG_EN, "[APP][EVT] disconnect, reason 0x%x\n", pEvt->terminate_reason);

// #if (BLE_APP_PM_ENABLE)
// 	 //user has push terminate packet to BLE TX buffer before deepsleep
// 	if(sendTerminate_before_enterDeep == 1 && !TEST_CONN_CURRENT_ENABLE){
// 		sendTerminate_before_enterDeep = 2;
// 		bls_ll_setAdvEnable(BLC_ADV_DISABLE);   //disable ADV
// 	}
// #endif


// #if (UI_LED_ENABLE && !TEST_CONN_CURRENT_ENABLE)
// 	gpio_write(GPIO_LED_RED, !LED_ON_LEVEL);  //red led off
// #endif

// 	advertise_begin_tick = clock_time();
// }




// /**
//  * @brief      callback function of LinkLayer Event "BLT_EV_FLAG_SUSPEND_EXIT"
//  * @param[in]  e - LinkLayer Event type
//  * @param[in]  p - data pointer of event
//  * @param[in]  n - data length of event
//  * @return     none
//  */
// void task_suspend_exit (u8 e, u8 *p, int n)
// {
// 	(void)e;(void)p;(void)n;
// 	rf_set_power_level_index (RF_POWER_P3dBm);
// }


// /**
//  * @brief      callback function of LinkLayer Event "BLT_EV_FLAG_DATA_LENGTH_EXCHANGE"
//  * @param[in]  e - LinkLayer Event type
//  * @param[in]  p - data pointer of event
//  * @param[in]  n - data length of event
//  * @return     none
//  */
// void	task_dle_exchange (u8 e, u8 *p, int n)
// {
// 	tlk_contr_evt_dataLenExg_t* pEvt = (tlk_contr_evt_dataLenExg_t*)p;
// 	tlkapi_send_string_data(APP_CONTR_EVENT_LOG_EN, "[APP][EVT] DLE exchange", &pEvt->connEffectiveMaxRxOctets, 4);
// }

// void ximBLE_blsInit()
// {
//     bls_app_registerEventCallback (BLT_EV_FLAG_CONNECT, &task_connect);
// 	bls_app_registerEventCallback (BLT_EV_FLAG_TERMINATE, &task_terminate);
// 	bls_app_registerEventCallback (BLT_EV_FLAG_SUSPEND_EXIT, &task_suspend_exit);
// 	bls_app_registerEventCallback (BLT_EV_FLAG_DATA_LENGTH_EXCHANGE, &task_dle_exchange);
//     bls_pm_setSuspendMask (SUSPEND_DISABLE);
// }