/********************************************************************************************************
 * @file	driver_8278.h
 *
 * @brief	This is the header file for B85
 *
 * <AUTHOR> Group
 * @date	May 8,2018
 *
 * @par     Copyright (c) 2018, Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *          All rights reserved.
 *
 *          Redistribution and use in source and binary forms, with or without
 *          modification, are permitted provided that the following conditions are met:
 *
 *              1. Redistributions of source code must retain the above copyright
 *              notice, this list of conditions and the following disclaimer.
 *
 *              2. Unless for usage inside a TELINK integrated circuit, redistributions
 *              in binary form must reproduce the above copyright notice, this list of
 *              conditions and the following disclaimer in the documentation and/or other
 *              materials provided with the distribution.
 *
 *              3. Neither the name of TELINK, nor the names of its contributors may be
 *              used to endorse or promote products derived from this software without
 *              specific prior written permission.
 *
 *              4. This software, with or without modification, must only be used with a
 *              TELINK integrated circuit. All other usages are subject to written permission
 *              from TELINK and different commercial license may apply.
 *
 *              5. Licensee shall be solely responsible for any claim to the extent arising out of or
 *              relating to such deletion(s), modification(s) or alteration(s).
 *
 *          THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *          ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *          WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 *          DISCLAIMED. IN NO EVENT SHALL COPYRIGHT HOLDER BE LIABLE FOR ANY
 *          DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *          (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 *          LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 *          ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *          (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 *          SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 *******************************************************************************************************/
#pragma once



#include "drivers/8278/bsp.h"
#include "drivers/8278/aes.h"
#include "drivers/8278/analog.h"
#include "drivers/8278/compiler.h"
#include "drivers/8278/register.h"
#include "drivers/8278/gpio.h"
#include "drivers/8278/pwm.h"
#include "drivers/8278/irq.h"
#include "drivers/8278/clock.h"
#include "drivers/8278/random.h"
#include "drivers/8278/flash.h"
#include "drivers/8278/rf_drv.h"
#include "drivers/8278/pm.h"
#include "drivers/8278/audio.h"
#include "drivers/8278/adc.h"
#include "drivers/8278/i2c.h"
#include "drivers/8278/spi.h"
#include "drivers/8278/uart.h"
#include "drivers/8278/register.h"
#include "drivers/8278/watchdog.h"
#include "drivers/8278/register.h"
#include "drivers/8278/dfifo.h"
#include "drivers/8278/dma.h"
#include "drivers/8278/emi.h"
#include "drivers/8278/timer.h"

#include "drivers/8278/s7816.h"
#include "drivers/8278/qdec.h"
#include "drivers/8278/lpc.h"

#include "drivers/8278/pke.h"

#include "drivers/8278/rf_pa.h"

#include "drivers/8278/flash/flash_type.h"
