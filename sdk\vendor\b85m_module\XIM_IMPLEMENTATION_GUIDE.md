# XIM 数据管道服务实现指南

## 概述

XIM数据管道服务已成功重构到独立的 `app_xim.c` 和 `app_xim.h` 文件中，并实现了10ms定时器自动发送数据通知的功能。

## 文件结构

```
b85_ble_sdk/vendor/b85m_module/
├── app_xim.h          # XIM服务头文件
├── app_xim.c          # XIM服务实现文件
├── app_att.c          # 属性表定义（包含XIM服务注册）
├── app_att.h          # 属性表头文件（包含句柄定义）
└── app_train.c        # 应用初始化（调用app_xim_init）
```

## 核心功能

### 1. 自动定时发送 (10ms)

- **定时器**: 每10ms触发一次
- **条件**: 仅在设备连接且客户端订阅通知时发送
- **数据**: 自动调用 `ximDataPipe_updateBuffer()` 更新数据后发送

### 2. 数据缓冲区管理

```c
// 数据缓冲区定义在 app_att.c 中（用于属性表静态初始化）
_attribute_data_retention_ u8 dataPipeValue[20] = {0};      // 20字节数据缓冲区
_attribute_data_retention_ u8 dataPipeValueCCC[2] = {0, 0}; // 客户端配置描述符 (CCC)
_attribute_data_retention_ u8 dataPipeCommand[20] = {0};    // 命令接收缓冲区

// 在 app_xim.c 中通过外部声明访问
extern u8 dataPipeValue[20];
extern u8 dataPipeValueCCC[2];
extern u8 dataPipeCommand[20];
```

### 3. 通知控制

- **订阅检测**: 自动检测客户端是否启用通知
- **连接状态**: 仅在连接状态下发送通知
- **数据更新**: 每次发送前自动更新数据

## API 接口

### 初始化函数

```c
void app_xim_init(void);  // 初始化XIM服务并启动10ms定时器
```

### 数据管理函数

```c
void ximDataPipe_updateValue(u8 *data, u8 len);     // 手动更新数据
u8 ximDataPipe_getValue(u8 *data, u8 maxLen);       // 获取当前数据
void ximDataPipe_updateBuffer(void);                // 自动数据更新（您需要实现）
```

### 通知函数

```c
int ximDataPipe_sendNotification(u8 *data, u8 len); // 手动发送通知
u8 ximDataPipe_isNotificationEnabled(void);         // 检查通知状态
```

### 命令处理函数

```c
u8 ximDataPipe_getCommand(u8 *data, u8 maxLen);     // 获取接收到的命令
int ximDataPipeCommand_onReceiveData(rf_packet_att_write_t *para); // 命令回调
```

## 数据更新实现

### 当前示例实现

`ximDataPipe_updateBuffer()` 函数中包含了一个示例实现：

```c
void ximDataPipe_updateBuffer(void)
{
    static u32 counter = 0;
    u8 buffer[20] = {0};
    
    // 数据结构示例:
    buffer[0] = 0x01;                    // 数据类型ID
    buffer[1] = counter & 0xFF;          // 计数器低字节
    buffer[2] = (counter >> 8) & 0xFF;   // 计数器高字节
    buffer[3] = battery_level_get();     // 电池电量
    buffer[4] = node_status;             // 节点状态
    buffer[5] = laser_status;            // 激光状态
    
    // 时间戳
    u32 timestamp = clock_time();
    buffer[6] = timestamp & 0xFF;
    buffer[7] = (timestamp >> 8) & 0xFF;
    buffer[8] = (timestamp >> 16) & 0xFF;
    buffer[9] = (timestamp >> 24) & 0xFF;
    
    // 更新数据管道值
    ximDataPipe_updateValue(buffer, sizeof(buffer));
    counter++;
}
```

### 自定义数据实现

您需要根据实际需求修改 `ximDataPipe_updateBuffer()` 函数：

```c
void ximDataPipe_updateBuffer(void)
{
    u8 buffer[20] = {0};
    
    // TODO: 在这里添加您的数据收集逻辑
    // 例如：
    // - 读取传感器数据
    // - 获取系统状态
    // - 处理业务逻辑数据
    // - 格式化数据包
    
    // 示例：自定义数据格式
    buffer[0] = 0xAA;                    // 数据包头
    buffer[1] = your_sensor_data_1;      // 传感器数据1
    buffer[2] = your_sensor_data_2;      // 传感器数据2
    buffer[3] = your_status_flags;       // 状态标志
    // ... 添加更多数据字段
    buffer[19] = calculate_checksum(buffer, 19); // 校验和
    
    ximDataPipe_updateValue(buffer, sizeof(buffer));
}
```

## 使用流程

### 1. 系统初始化

```c
void user_init_train() {
    // ... 其他初始化代码
    
    // 初始化XIM数据管道服务
    app_xim_init();  // 这会启动10ms定时器
}
```

### 2. 客户端连接和订阅

1. 客户端连接到设备
2. 发现XIM数据管道服务 (UUID: 0xF000)
3. 启用数据值特征的通知 (UUID: 0xF001)
4. 系统自动开始每10ms发送数据

### 3. 数据流程

```
每10ms定时器触发
    ↓
检查连接状态和订阅状态
    ↓
调用 ximDataPipe_updateBuffer()
    ↓
更新20字节数据缓冲区
    ↓
发送BLE通知给客户端
```

## 配置选项

### 修改发送频率

在 `app_xim_init()` 函数中修改定时器间隔：

```c
// 修改为20ms (20000us)
blt_soft_timer_add(&xim_timer_callback, 20000);

// 修改为100ms (100000us)
blt_soft_timer_add(&xim_timer_callback, 100000);
```

### 启用/禁用自动发送

```c
// 禁用自动发送
ximDataPipe_setNotificationEnabled(0);

// 启用自动发送
ximDataPipe_setNotificationEnabled(1);
```

## 调试和测试

### 1. 检查通知状态

```c
if(ximDataPipe_isNotificationEnabled()) {
    // 通知已启用
} else {
    // 通知未启用
}
```

### 2. 手动发送测试数据

```c
u8 test_data[] = {0x01, 0x02, 0x03, 0x04, 0x05};
ximDataPipe_sendNotification(test_data, sizeof(test_data));
```

### 3. 监控数据发送

可以在 `xim_timer_callback()` 函数中添加调试代码：

```c
int xim_timer_callback(void)
{
    if(device_in_connection_state && xim_notification_enabled) {
        ximDataPipe_updateBuffer();
        
        // 调试输出
        printf("Sending XIM data notification\n");
        
        ximDataPipe_sendNotification(dataPipeValue, sizeof(dataPipeValue));
    }
    return 0;
}
```

## 注意事项

1. **性能影响**: 10ms高频发送可能影响系统性能，请根据实际需求调整
2. **数据同步**: 确保数据更新的原子性，避免在更新过程中被中断
3. **内存使用**: 数据缓冲区使用静态分配，注意内存使用
4. **错误处理**: 添加适当的错误检查和处理机制
5. **功耗考虑**: 高频通信会增加功耗，在低功耗应用中需要权衡

## 扩展功能

### 1. 数据压缩

对于复杂数据，可以实现数据压缩：

```c
u8 compressed_data[20];
u8 compressed_len = compress_data(raw_data, raw_len, compressed_data);
ximDataPipe_updateValue(compressed_data, compressed_len);
```

### 2. 数据分片

对于大于20字节的数据，可以实现分片传输：

```c
typedef struct {
    u8 fragment_id;
    u8 total_fragments;
    u8 data[18];
} xim_fragment_t;
```

### 3. 动态频率调整

根据数据变化频率动态调整发送间隔：

```c
void adjust_notification_frequency(u32 new_interval_us) {
    blt_soft_timer_delete(&xim_timer_callback);
    blt_soft_timer_add(&xim_timer_callback, new_interval_us);
}
```
