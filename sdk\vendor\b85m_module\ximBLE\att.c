/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : att.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2025/09/12    yuanpai       N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/
#include "tl_common.h"

#include "stack/ble/ble.h"
// #include "app.h"
// #include "app_att.h"

#include "ximBLE.h"

#define XIM_ATTRIBUTES_MAX 64
static attribute_t xim_attributes[XIM_ATTRIBUTES_MAX];

const uint16_t _GAP_APPEARE_UNKNOWN = GAP_APPEARE_UNKNOWN;  //0x0000
const uint16_t _GATT_UUID_PRIMARY_SERVICE = GATT_UUID_PRIMARY_SERVICE; //0x2800
const uint16_t _SERVICE_UUID_GENERIC_ACCESS = SERVICE_UUID_GENERIC_ACCESS; //0x1800
const uint16_t _SERVICE_UUID_GENERIC_ATTRIBUTE = SERVICE_UUID_GENERIC_ATTRIBUTE; //0x1801
const uint16_t _SERVICE_UUID_DEVICE_INFORMATION = SERVICE_UUID_DEVICE_INFORMATION;// 0x180A
const uint16_t _SERVICE_UUID_BATTERY = SERVICE_UUID_BATTERY; //0x180F
const uint16_t _GATT_UUID_CHARACTER = GATT_UUID_CHARACTER; //0x2803
const uint16_t _GATT_UUID_CHAR_USER_DESC = GATT_UUID_CHAR_USER_DESC; //0x2901
const uint16_t _GATT_UUID_CLIENT_CHAR_CFG = GATT_UUID_CLIENT_CHAR_CFG; //0x2902
const uint16_t _GATT_UUID_APPEARANCE = GATT_UUID_APPEARANCE; //0x2A01
const uint16_t _GATT_UUID_PERI_CONN_PARAM = GATT_UUID_PERI_CONN_PARAM; //0x2A04
const uint16_t _GATT_UUID_SERVICE_CHANGE = GATT_UUID_SERVICE_CHANGE; //0x2A05
const uint16_t _GATT_UUID_DEVICE_NAME = GATT_UUID_DEVICE_NAME; //0x2A00
const uint16_t _CHARACTERISTIC_UUID_BATTERY_LEVEL = CHARACTERISTIC_UUID_BATTERY_LEVEL; //0x2A19
const uint16_t _CHARACTERISTIC_UUID_MANU_NAME_STRING     = CHARACTERISTIC_UUID_MANU_NAME_STRING     ; //
const uint16_t _CHARACTERISTIC_UUID_MODEL_NUM_STRING     = CHARACTERISTIC_UUID_MODEL_NUM_STRING     ; //
const uint16_t _CHARACTERISTIC_UUID_SERIAL_NUM_STRING    = CHARACTERISTIC_UUID_SERIAL_NUM_STRING    ; //
const uint16_t _CHARACTERISTIC_UUID_HW_REVISION_STRING   = CHARACTERISTIC_UUID_HW_REVISION_STRING   ; //
const uint16_t _CHARACTERISTIC_UUID_FW_REVISION_STRING   = CHARACTERISTIC_UUID_FW_REVISION_STRING   ; //
const uint16_t _CHARACTERISTIC_UUID_SW_REVISION_STRING   = CHARACTERISTIC_UUID_SW_REVISION_STRING   ; //
const uint16_t _CHARACTERISTIC_UUID_SYSTEM_ID            = CHARACTERISTIC_UUID_SYSTEM_ID            ; //
const uint16_t _CHARACTERISTIC_UUID_IEEE_11073_CERT_LIST = CHARACTERISTIC_UUID_IEEE_11073_CERT_LIST ; //
const uint16_t _CHARACTERISTIC_UUID_PNP_ID               = CHARACTERISTIC_UUID_PNP_ID               ; //

static void add_attribute(attribute_t *attributes, const attribute_t *attribute)
{
    attributes[0].attNum++;
    uint16_t index = attributes[0].attNum;
    
    if(index >= XIM_ATTRIBUTES_MAX) {
        // tlkapi_printf(1, "ERROR: Attribute table overflow!\r\n");
        return;
    }
    
    memcpy(&attributes[index], attribute, sizeof(attribute_t));
}

static uint16_t get_att_handle_id_on_next_att(attribute_t *attributes)
{
    // printf("add handle id = %d\r\n", attributes[0].attNum + 2);
    return attributes[0].attNum + 2;
}

void att_add_gap(attribute_t *attributes)
{
    static gatt_char_t deviceNameChar = {CHAR_PROP_READ, 0, GATT_UUID_DEVICE_NAME};
    static gatt_char_t appearanceChar = {CHAR_PROP_READ, 0, GATT_UUID_APPEARANCE};
    static gatt_char_t periConnParamChar = {CHAR_PROP_READ, 0, GATT_UUID_PERI_CONN_PARAM};
    
    add_attribute(attributes, &(const attribute_t){7,ATT_PERMISSIONS_READ,2,2,(u8*)&_GATT_UUID_PRIMARY_SERVICE, (u8*)&_SERVICE_UUID_GENERIC_ACCESS, 0, 0});
    deviceNameChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    add_attribute(attributes, &(const attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&deviceNameChar, 0, 0});
    add_attribute(attributes, &(const attribute_t){0,ATT_PERMISSIONS_READ,2,(u8)(strlen ((const char *)ximBleConfig.deviceName)), (u8*)&_GATT_UUID_DEVICE_NAME, (u8*)(ximBleConfig.deviceName), 0, 0});
    appearanceChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    add_attribute(attributes, &(const attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&appearanceChar, 0, 0});
    add_attribute(attributes, &(const attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof (u16), (u8*)&_GATT_UUID_APPEARANCE,(u8*)&_GAP_APPEARE_UNKNOWN, 0, 0});
    periConnParamChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    add_attribute(attributes, &(const attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&periConnParamChar, 0, 0});
    add_attribute(attributes, &(const attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gap_periConnectParams_t),(u8*)&_GATT_UUID_PERI_CONN_PARAM, (u8*)&((gap_periConnectParams_t){20, 40, 0, 1000}), 0, 0});
}

void att_add_gatt(attribute_t *attributes)
{
    static gatt_char_t serviceChangeChar = {CHAR_PROP_INDICATE, 0, GATT_UUID_SERVICE_CHANGE};

    _attribute_data_retention_	static u16 serviceChangeVal[2] = {0};
    _attribute_data_retention_	static u8 serviceChangeCCC[2] = {0,0};

    add_attribute(attributes, &(attribute_t){4,ATT_PERMISSIONS_READ,2,2,(u8*)&_GATT_UUID_PRIMARY_SERVICE, (u8*)&_SERVICE_UUID_GENERIC_ATTRIBUTE, 0, 0});
    serviceChangeChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
	add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&serviceChangeChar, 0, 0});
	add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof (serviceChangeVal), (u8*)&_GATT_UUID_SERVICE_CHANGE,(u8*)(&serviceChangeVal), 0, 0});
	add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_RDWR,2,sizeof (serviceChangeCCC),(u8*)&_GATT_UUID_CLIENT_CHAR_CFG, (u8*)(serviceChangeCCC), 0, 0});
}

void att_add_dis(attribute_t *attributes)
{
    static gatt_char_t maufacturerNameChar = {CHAR_PROP_READ, 0, CHARACTERISTIC_UUID_MANU_NAME_STRING};
    static gatt_char_t modelNumberChar = {CHAR_PROP_READ, 0, CHARACTERISTIC_UUID_MODEL_NUM_STRING};
    static gatt_char_t serialNumberChar = {CHAR_PROP_READ, 0, CHARACTERISTIC_UUID_SERIAL_NUM_STRING};
    static gatt_char_t hardwareRevisionChar = {CHAR_PROP_READ, 0, CHARACTERISTIC_UUID_HW_REVISION_STRING};
    static gatt_char_t firmwareRevisionChar = {CHAR_PROP_READ, 0, CHARACTERISTIC_UUID_FW_REVISION_STRING};
    static gatt_char_t softwareRevisionChar = {CHAR_PROP_READ, 0, CHARACTERISTIC_UUID_SW_REVISION_STRING};

    add_attribute(attributes, &(attribute_t){13,ATT_PERMISSIONS_READ,2,2,(u8*)&_GATT_UUID_PRIMARY_SERVICE, (u8*)&_SERVICE_UUID_DEVICE_INFORMATION, 0, 0});
    maufacturerNameChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&maufacturerNameChar, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,strlen((const char *)ximBleConfig.manufacturerName),(u8*)&_CHARACTERISTIC_UUID_MANU_NAME_STRING,(u8*)(ximBleConfig.manufacturerName), 0, 0});
    modelNumberChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&modelNumberChar, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,strlen((const char *)ximBleConfig.modelNumber),(u8*)&_CHARACTERISTIC_UUID_MODEL_NUM_STRING,(u8*)(ximBleConfig.modelNumber), 0, 0});
    serialNumberChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&serialNumberChar, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,strlen((const char *)ximBleConfig.serialNumber),(u8*)&_CHARACTERISTIC_UUID_SERIAL_NUM_STRING,(u8*)(ximBleConfig.serialNumber), 0, 0});
    hardwareRevisionChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&hardwareRevisionChar, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,strlen((const char *)ximBleConfig.hardwareRevision),(u8*)&_CHARACTERISTIC_UUID_HW_REVISION_STRING,(u8*)(ximBleConfig.hardwareRevision), 0, 0});
    firmwareRevisionChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&firmwareRevisionChar, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,strlen((const char *)ximBleConfig.firmwareRevision),(u8*)&_CHARACTERISTIC_UUID_FW_REVISION_STRING,(u8*)(ximBleConfig.firmwareRevision), 0, 0});
    softwareRevisionChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&softwareRevisionChar, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,strlen((const char *)ximBleConfig.softwareRevision),(u8*)&_CHARACTERISTIC_UUID_SW_REVISION_STRING,(u8*)(ximBleConfig.softwareRevision), 0, 0});
}

void att_add_batteryService(attribute_t *attributes)
{
    static gatt_char_t batteryLevelChar = {CHAR_PROP_READ | CHAR_PROP_NOTIFY, 0, CHARACTERISTIC_UUID_BATTERY_LEVEL};
    _attribute_data_retention_	static u8 batteryValueInCCC[2] = {0, 0};
    _attribute_data_retention_	static u8 batteryValue[1] 	= {99};

    add_attribute(attributes, &(attribute_t){3,ATT_PERMISSIONS_READ,2,2,(u8*)&_GATT_UUID_PRIMARY_SERVICE, (u8*)&_SERVICE_UUID_BATTERY, 0, 0});
    batteryLevelChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&batteryLevelChar, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof (batteryValue),(u8*)&_CHARACTERISTIC_UUID_BATTERY_LEVEL,(u8*)(batteryValue), 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_RDWR,2,sizeof (batteryValueInCCC),(u8*)&_GATT_UUID_CLIENT_CHAR_CFG,(u8*)(batteryValueInCCC), 0, 0});
}

void att_add_xim_dataPipe(attribute_t *attributes)
{
    static const uint16_t _GATT_UUID_XIM_DATAPIPE_SERVICE = 0xF000;
    static const uint16_t _CHARACTERISTIC_UUID_DATA_PIPE_VALUE = 0xF001;
    static const uint16_t _CHARACTERISTIC_UUID_DATA_PIPE_COMMAND = 0xF002;

    static gatt_char_t dataPipeValueChar = {CHAR_PROP_READ | CHAR_PROP_NOTIFY, 0, 0xF001};
    static gatt_char_t dataPipeCommandChar = {CHAR_PROP_WRITE_WITHOUT_RSP | CHAR_PROP_WRITE, 0, 0xF002};

    _attribute_data_retention_	static u8 dataPipeValue[20] = {0};
    _attribute_data_retention_	static u8 dataPipeValueCCC[2] = {0, 0};
    _attribute_data_retention_	static u8 dataPipeCommand[20] = {0};
    // _attribute_data_retention_	static u8 dataPipeCommandCCC[2] = {0,0};

    add_attribute(attributes, &(attribute_t){6,ATT_PERMISSIONS_READ,2,2,(u8*)&_GATT_UUID_PRIMARY_SERVICE, (u8*)&_GATT_UUID_XIM_DATAPIPE_SERVICE, 0, 0});
    dataPipeValueChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    ximBleConfig.ximDataPipeValueHandle = dataPipeValueChar.gatt_char_handle;
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&dataPipeValueChar, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof (dataPipeValue),(u8*)&_CHARACTERISTIC_UUID_DATA_PIPE_VALUE,(u8*)(dataPipeValue), 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_RDWR,2,sizeof (dataPipeValueCCC),(u8*)&_GATT_UUID_CLIENT_CHAR_CFG,(u8*)(dataPipeValueCCC), 0, 0});
    dataPipeCommandChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    ximBleConfig.ximDataPipeCommandHandle = dataPipeCommandChar.gatt_char_handle;
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&dataPipeCommandChar, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_WRITE,2,sizeof (dataPipeCommand),(u8*)&_CHARACTERISTIC_UUID_DATA_PIPE_COMMAND,(u8*)(dataPipeCommand), ximBleConfig.ximDataPipeCommandCallback, 0});
    // add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_RDWR,2,sizeof (dataPipeCommandCCC),(u8*)&_GATT_UUID_CLIENT_CHAR_CFG}),(u8*)(dataPipeCommandCCC), 0, 0});
}

void att_add_xim_configPipe(attribute_t *attributes)
{
    static const uint16_t _GATT_UUID_XIM_DATAPIPE_SERVICE = 0xF200;
    static const uint16_t _CHARACTERISTIC_UUID_DATA_PIPE_VALUE = 0xF201;
    static const uint16_t _CHARACTERISTIC_UUID_DATA_PIPE_COMMAND = 0xF202;

    static gatt_char_t dataPipeValueChar = {CHAR_PROP_READ | CHAR_PROP_NOTIFY, 0, 0xF201};
    static gatt_char_t dataPipeCommandChar = {CHAR_PROP_WRITE_WITHOUT_RSP | CHAR_PROP_WRITE, 0, 0xF202};

    _attribute_data_retention_	static u8 dataPipeValue[20] = {0};
    _attribute_data_retention_	static u8 dataPipeValueCCC[2] = {0, 0};
    _attribute_data_retention_	static u8 dataPipeCommand[20] = {0};
    // _attribute_data_retention_	static u8 dataPipeCommandCCC[2] = {0,0};

    add_attribute(attributes, &(attribute_t){6,ATT_PERMISSIONS_READ,2,2,(u8*)&_GATT_UUID_PRIMARY_SERVICE, (u8*)&_GATT_UUID_XIM_DATAPIPE_SERVICE, 0, 0});
    dataPipeValueChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    ximBleConfig.ximConfigPipeValueHandle = dataPipeValueChar.gatt_char_handle;
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&dataPipeValueChar, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof (dataPipeValue),(u8*)&_CHARACTERISTIC_UUID_DATA_PIPE_VALUE,(u8*)(dataPipeValue), 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_RDWR,2,sizeof (dataPipeValueCCC),(u8*)&_GATT_UUID_CLIENT_CHAR_CFG,(u8*)(dataPipeValueCCC), 0, 0});
    dataPipeCommandChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    ximBleConfig.ximConfigPipeCommandHandle = dataPipeCommandChar.gatt_char_handle;
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&dataPipeCommandChar, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_WRITE,2,sizeof (dataPipeCommand),(u8*)&_CHARACTERISTIC_UUID_DATA_PIPE_COMMAND,(u8*)(dataPipeCommand), ximBleConfig.ximConfigPipeCommandCallback, 0});

}

void att_add_xim_calibPipe(attribute_t *attributes)
{
    static const uint16_t _GATT_UUID_XIM_DATAPIPE_SERVICE = 0xF010;
    static const uint16_t _CHARACTERISTIC_UUID_DATA_PIPE_VALUE = 0xF011;
    static const uint16_t _CHARACTERISTIC_UUID_DATA_PIPE_COMMAND = 0xF012;

    static gatt_char_t dataPipeValueChar = {CHAR_PROP_READ | CHAR_PROP_NOTIFY, 0, 0xF011};
    static gatt_char_t dataPipeCommandChar = {CHAR_PROP_WRITE_WITHOUT_RSP | CHAR_PROP_WRITE, 0, 0xF012};

    _attribute_data_retention_	static u8 dataPipeValue[20] = {0};
    _attribute_data_retention_	static u8 dataPipeValueCCC[2] = {0, 0};
    _attribute_data_retention_	static u8 dataPipeCommand[20] = {0};
    // _attribute_data_retention_	static u8 dataPipeCommandCCC[2] = {0,0};

    add_attribute(attributes, &(attribute_t){6,ATT_PERMISSIONS_READ,2,2,(u8*)&_GATT_UUID_PRIMARY_SERVICE, (u8*)&_GATT_UUID_XIM_DATAPIPE_SERVICE, 0, 0});
    dataPipeValueChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    ximBleConfig.ximCalibPipeValueHandle = dataPipeValueChar.gatt_char_handle;
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&dataPipeValueChar, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof (dataPipeValue),(u8*)&_CHARACTERISTIC_UUID_DATA_PIPE_VALUE,(u8*)(dataPipeValue), 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_RDWR,2,sizeof (dataPipeValueCCC),(u8*)&_GATT_UUID_CLIENT_CHAR_CFG,(u8*)(dataPipeValueCCC), 0, 0});
    dataPipeCommandChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    ximBleConfig.ximCalibPipeCommandHandle = dataPipeCommandChar.gatt_char_handle;
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&dataPipeCommandChar, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_WRITE,2,sizeof (dataPipeCommand),(u8*)&_CHARACTERISTIC_UUID_DATA_PIPE_COMMAND,(u8*)(dataPipeCommand), ximBleConfig.ximCalibPipeCommandCallback, 0});


}

void att_add_xim_calibCheckPipe(attribute_t *attributes)
{
    static const uint16_t _GATT_UUID_XIM_DATAPIPE_SERVICE = 0xF020;
    static const uint16_t _CHARACTERISTIC_UUID_DATA_PIPE_VALUE = 0xF021;
    static const uint16_t _CHARACTERISTIC_UUID_DATA_PIPE_COMMAND = 0xF022;

    static gatt_char_t dataPipeValueChar = {CHAR_PROP_READ | CHAR_PROP_NOTIFY, 0, 0xF021};
    static gatt_char_t dataPipeCommandChar = {CHAR_PROP_WRITE_WITHOUT_RSP | CHAR_PROP_WRITE, 0, 0xF022};

    _attribute_data_retention_	static u8 dataPipeValue[20] = {0};
    _attribute_data_retention_	static u8 dataPipeValueCCC[2] = {0, 0};
    _attribute_data_retention_	static u8 dataPipeCommand[20] = {0};
    // _attribute_data_retention_	static u8 dataPipeCommandCCC[2] = {0,0};

    add_attribute(attributes, &(attribute_t){6,ATT_PERMISSIONS_READ,2,2,(u8*)&_GATT_UUID_PRIMARY_SERVICE, (u8*)&_GATT_UUID_XIM_DATAPIPE_SERVICE, 0, 0});
    dataPipeValueChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    ximBleConfig.ximCalibCheckPipeValueHandle = dataPipeValueChar.gatt_char_handle;
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&dataPipeValueChar, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof (dataPipeValue),(u8*)&_CHARACTERISTIC_UUID_DATA_PIPE_VALUE,(u8*)(dataPipeValue), 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_RDWR,2,sizeof (dataPipeValueCCC),(u8*)&_GATT_UUID_CLIENT_CHAR_CFG,(u8*)(dataPipeValueCCC), 0, 0});
    dataPipeCommandChar.gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    ximBleConfig.ximCalibCheckPipeCommandHandle = dataPipeCommandChar.gatt_char_handle;
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(gatt_char_t),(u8*)&_GATT_UUID_CHARACTER, (u8*)&dataPipeCommandChar, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_WRITE,2,sizeof (dataPipeCommand),(u8*)&_CHARACTERISTIC_UUID_DATA_PIPE_COMMAND,(u8*)(dataPipeCommand), ximBleConfig.ximCalibCheckPipeCommandCallback, 0});

}

void att_add_tlk_spp(attribute_t *attributes)
{
	// {8,ATT_PERMISSIONS_READ,2,16,(u8*)(&my_primaryServiceUUID), 	(u8*)(&TelinkSppServiceUUID), 0},
	// {0,ATT_PERMISSIONS_READ,2,sizeof(TelinkSppDataClient2ServerCharVal),(u8*)(&my_characterUUID), 		(u8*)(TelinkSppDataClient2ServerCharVal), 0},				//prop
	// {0,ATT_PERMISSIONS_RDWR,16,sizeof(SppDataClient2ServerData),(u8*)(&TelinkSppDataClient2ServerUUID), (u8*)(SppDataClient2ServerData), (att_readwrite_callback_t)&module_onReceiveData},	//value
	// {0,ATT_PERMISSIONS_READ,2,sizeof(TelinkSppDataServer2ClientCharVal),(u8*)(&my_characterUUID), 		(u8*)(TelinkSppDataServer2ClientCharVal), 0},				//prop
	// {0,ATT_PERMISSIONS_READ,16,sizeof(SppDataServer2ClientData),(u8*)(&TelinkSppDataServer2ClientUUID), (u8*)(SppDataServer2ClientData), 0},	//value
	// {0,ATT_PERMISSIONS_RDWR,2,2,(u8*)&clientCharacterCfgUUID,(u8*)(&SppDataServer2ClientDataCCC)},
    uint16_t gatt_char_handle = 0;
    
    static uint8_t TelinkSppDataServer2ClientCharVal[19] = { CHAR_PROP_NOTIFY,0, 0,TELINK_SPP_DATA_SERVER2CLIENT};
    static uint8_t TelinkSppDataClient2ServerCharVal[19] = { CHAR_PROP_WRITE_WITHOUT_RSP,0, 0,TELINK_SPP_DATA_CLIENT2SERVER};

    static const u8 TelinkSppServiceUUID[16]	      	    = WRAPPING_BRACES(TELINK_SPP_UUID_SERVICE);
    static const u8 TelinkSppDataServer2ClientUUID[16]      = WRAPPING_BRACES(TELINK_SPP_DATA_SERVER2CLIENT);
    static const u8 TelinkSppDataClient2ServerUUID[16]      = WRAPPING_BRACES(TELINK_SPP_DATA_CLIENT2SERVER);

    _attribute_data_retention_	static u8 SppDataServer2ClientData[16] = {0};
    _attribute_data_retention_	static u8 SppDataServer2ClientDataCCC[2] = {0, 0};
    _attribute_data_retention_	static u8 SppDataClient2ServerData[16] = {0};
    _attribute_data_retention_	static u8 SppDataClient2ServerDataCCC[2] = {0, 0};

    add_attribute(attributes, &(attribute_t){6,ATT_PERMISSIONS_READ,2,16,(u8*)&_GATT_UUID_PRIMARY_SERVICE, (u8*)&TelinkSppServiceUUID, 0, 0});
    gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    TelinkSppDataClient2ServerCharVal[1] = U16_LO(gatt_char_handle);
    TelinkSppDataClient2ServerCharVal[2] = U16_HI(gatt_char_handle);
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(TelinkSppDataClient2ServerCharVal),(u8*)&_GATT_UUID_CHARACTER, (u8*)&TelinkSppDataClient2ServerCharVal, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_RDWR,16,sizeof (SppDataClient2ServerData),(u8*)&TelinkSppDataClient2ServerUUID,(u8*)(SppDataClient2ServerData), ximBleConfig.tlkSppClient2ServerCallback, 0});
    gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    TelinkSppDataServer2ClientCharVal[1] = U16_LO(gatt_char_handle);
    TelinkSppDataServer2ClientCharVal[2] = U16_HI(gatt_char_handle);
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,2,sizeof(TelinkSppDataServer2ClientCharVal),(u8*)&_GATT_UUID_CHARACTER, (u8*)&TelinkSppDataServer2ClientCharVal, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ,16,sizeof (SppDataServer2ClientData),(u8*)&TelinkSppDataServer2ClientUUID,(u8*)(SppDataServer2ClientData), 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_RDWR,2,sizeof (SppDataServer2ClientDataCCC),(u8*)&_GATT_UUID_CLIENT_CHAR_CFG,(u8*)(SppDataServer2ClientDataCCC), 0, 0});
}

void att_add_ota(attribute_t *attributes)
{
    uint16_t gatt_char_handle = 0;
    static uint8_t ota_char_val[19] = { CHAR_PROP_READ | CHAR_PROP_WRITE_WITHOUT_RSP | CHAR_PROP_NOTIFY | CHAR_PROP_WRITE,0, 0,TELINK_SPP_DATA_OTA};
    _attribute_data_retention_ static u8 otaData = 0x00;
    _attribute_data_retention_ static u8 otaDataCCC[2] = {0,0};

    static const uint8_t _TELINK_OTA_UUID_SERVICE[] = WRAPPING_BRACES(TELINK_OTA_UUID_SERVICE);
    static const uint8_t _TELINK_SPP_DATA_OTA[] = WRAPPING_BRACES(TELINK_SPP_DATA_OTA);

    add_attribute(attributes, &(attribute_t){5,ATT_PERMISSIONS_READ, 2, 16, (u8*)&_GATT_UUID_PRIMARY_SERVICE, 	(u8*)_TELINK_OTA_UUID_SERVICE, 0, 0});
    gatt_char_handle = get_att_handle_id_on_next_att(attributes);
    ota_char_val[1] = U16_LO(gatt_char_handle);
    ota_char_val[2] = U16_HI(gatt_char_handle);
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ, 2, 19, (u8*)&_GATT_UUID_CHARACTER, (u8*)ota_char_val, 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_RDWR,16,sizeof(otaData),(u8*)_TELINK_SPP_DATA_OTA,	(&otaData), &otaWrite, NULL});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_RDWR,2,sizeof(otaDataCCC),(u8*)&_GATT_UUID_CLIENT_CHAR_CFG, (u8*)(otaDataCCC), 0, 0});
    add_attribute(attributes, &(attribute_t){0,ATT_PERMISSIONS_READ, 2,(u8)(strlen ((const char *)ximBleConfig.deviceOtaName)),(u8*)&_GATT_UUID_CHAR_USER_DESC, (u8*)(ximBleConfig.deviceOtaName), 0, 0});
}


void xim_att_init()
{
    memset(xim_attributes, 0, sizeof(xim_attributes));

    att_add_gap(xim_attributes);
    att_add_gatt(xim_attributes);
    att_add_dis(xim_attributes);
    att_add_batteryService(xim_attributes);

    att_add_xim_dataPipe(xim_attributes);
    att_add_xim_configPipe(xim_attributes);
    att_add_xim_calibPipe(xim_attributes);
    att_add_xim_calibCheckPipe(xim_attributes);
    
    att_add_tlk_spp(xim_attributes);

    att_add_ota(xim_attributes);

    bls_att_setAttributeTable((u8 *)xim_attributes);
}


