/********************************************************************************************************
 * @file	app_xim.c
 *
 * @brief	This is the source file for XIM Data Pipe Service
 *
 * <AUTHOR> GROUP
 * @date	2024
 *
 * @par     Copyright (c) 2024, Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *          All rights reserved.
 *
 *******************************************************************************************************/

#include "tl_common.h"
#include "drivers.h"
#include "stack/ble/ble.h"
#include "app.h"
#include "app_att.h"
#include "app_xim.h"
#include "app_train.h"

// XIM Data Pipe Service data buffers (defined in app_att.c)
extern u8 dataPipeValue[20];
extern u8 dataPipeValueCCC[2];
extern u8 dataPipeCommand[20];

// Timer and notification control
static u32 xim_timer_tick = 0;
static u8 xim_notification_enabled = 0;

// External connection state and variables
extern u32 device_in_connection_state;
extern u8 node_status;
extern u8 laser_status;

/**
 * @brief      10ms timer callback for XIM data pipe notifications
 * @param[in]  none
 * @return     0
 */
int xim_timer_callback(void)
{
    // Check if device is connected and notifications are enabled
    if(device_in_connection_state && xim_notification_enabled) {
        // Update buffer with latest data
        ximDataPipe_updateBuffer();

        // Send current buffer data as notification
        ximDataPipe_sendNotification(dataPipeValue, sizeof(dataPipeValue));
    }
    return 0;
}

/**
 * @brief      Initialize XIM data pipe service
 * @param[in]  none
 * @return     none
 */
void app_xim_init(void)
{
    // Initialize data buffer with default values
    // u8 init_data[] = "XIM_READY";
    // ximDataPipe_updateValue(init_data, sizeof(init_data)-1);

    // Start 10ms timer for periodic notifications
    blt_soft_timer_add(&xim_timer_callback, 10000); // 10ms = 10000us
}

/**
 * @brief      Update XIM data pipe value
 * @param[in]  data - pointer to data buffer
 * @param[in]  len - data length (max 20 bytes)
 * @return     none
 */
void ximDataPipe_updateValue(u8 *data, u8 len)
{
    if(len > sizeof(dataPipeValue)) len = sizeof(dataPipeValue);

    // Clear buffer first
    memset(dataPipeValue, 0, sizeof(dataPipeValue));

    // Copy new data
    if(data && len > 0) {
        memcpy(dataPipeValue, data, len);
    }
}

/**
 * @brief      Get XIM data pipe value
 * @param[in]  data - pointer to buffer to store data
 * @param[in]  maxLen - maximum buffer size
 * @return     actual data length
 */
u8 ximDataPipe_getValue(u8 *data, u8 maxLen)
{
    u8 len = (maxLen < sizeof(dataPipeValue)) ? maxLen : sizeof(dataPipeValue);
    if(data) {
        memcpy(data, dataPipeValue, len);
    }
    return len;
}

/**
 * @brief      Send XIM data pipe notification to connected client
 * @param[in]  data - pointer to data buffer
 * @param[in]  len - data length (max 20 bytes)
 * @return     0 if success, non-zero if failed
 */
int ximDataPipe_sendNotification(u8 *data, u8 len)
{
    // Check if notifications are enabled (CCC descriptor bit 0 should be set)
    if(dataPipeValueCCC[0] & 0x01) {
        if(len > sizeof(dataPipeValue)) len = sizeof(dataPipeValue);

        // Update the value first if data is provided
        if(data) {
            memcpy(dataPipeValue, data, len);
        }

        // Send notification
        return bls_att_pushNotifyData(XIM_DataPipe_Value_DP_H, dataPipeValue, len);
    }
    return 0;
}

/**
 * @brief      Get last received XIM data pipe command
 * @param[in]  data - pointer to buffer to store command data
 * @param[in]  maxLen - maximum buffer size
 * @return     actual command data length
 */
u8 ximDataPipe_getCommand(u8 *data, u8 maxLen)
{
    u8 len = (maxLen < sizeof(dataPipeCommand)) ? maxLen : sizeof(dataPipeCommand);
    if(data) {
        memcpy(data, dataPipeCommand, len);
    }
    return len;
}

/**
 * @brief      XIM data pipe command write callback
 * @param[in]  para - rf_packet_att_write_t
 * @return     0
 */
int ximDataPipeCommand_onReceiveData(rf_packet_att_write_t *para)
{
    u8 len = para->l2capLen - 3;
    if(len > 0 && len <= sizeof(dataPipeCommand))
    {
        // Copy received data to dataPipeCommand buffer
        memcpy(dataPipeCommand, &(para->value), len);

        // Process the command data here
        // You can add your custom command processing logic
        // For example, echo the command back as a notification
        ximDataPipe_sendNotification(dataPipeCommand, len);
    }

    return 0;
}

/**
 * @brief      Check if XIM data pipe notifications are enabled
 * @param[in]  none
 * @return     1 if enabled, 0 if disabled
 */
u8 ximDataPipe_isNotificationEnabled(void)
{
    return (dataPipeValueCCC[0] & 0x01) ? 1 : 0;
}

/**
 * @brief      Set XIM data pipe notification state (called when CCC is written)
 * @param[in]  enabled - 1 to enable, 0 to disable
 * @return     none
 */
void ximDataPipe_setNotificationEnabled(u8 enabled)
{
    xim_notification_enabled = enabled ? 1 : 0;

    // Also update the CCC value
    if(enabled) {
        dataPipeValueCCC[0] |= 0x01;
    } else {
        dataPipeValueCCC[0] &= ~0x01;
    }
}



/**
 * @brief      Update XIM data buffer with sensor/status information
 * @param[in]  none
 * @return     none
 * @note       This is a placeholder function for you to implement your data update logic
 */
void ximDataPipe_updateBuffer(void)
{
    // TODO: Replace this with your actual data collection logic
    // This is just an example showing how to structure the data

    static u32 counter = 0;
    u8 buffer[20] = {0};

    // Example data structure:
    // Byte 0: Data type/ID
    // Byte 1-2: Counter (little endian)
    // Byte 3: Battery level (you can get this from battery service)
    // Byte 4: Node status
    // Byte 5: Laser status
    // Byte 6-19: Additional data as needed

    buffer[0] = 0x01;  // Data type ID
    buffer[1] = counter & 0xFF;
    buffer[2] = (counter >> 8) & 0xFF;
    buffer[3] = battery_level_get();  // Get current battery level
    buffer[4] = node_status;          // External variable from app_train.c
    buffer[5] = laser_status;         // External variable from app_train.c

    // Add timestamp or other data
    u32 timestamp = clock_time();
    buffer[6] = timestamp & 0xFF;
    buffer[7] = (timestamp >> 8) & 0xFF;
    buffer[8] = (timestamp >> 16) & 0xFF;
    buffer[9] = (timestamp >> 24) & 0xFF;

    // Update the data pipe value
    ximDataPipe_updateValue(buffer, sizeof(buffer));

    counter++;
}

/**
 * @brief      Test function to verify XIM data pipe functionality
 * @param[in]  none
 * @return     none
 */
void ximDataPipe_test(void)
{
    // Test data update
    u8 test_data[] = "TEST_XIM_DATA";
    ximDataPipe_updateValue(test_data, sizeof(test_data)-1);

    // Test manual notification (if connected and subscribed)
    if(device_in_connection_state && ximDataPipe_isNotificationEnabled()) {
        ximDataPipe_sendNotification(NULL, 20); // Send current buffer
    }
}