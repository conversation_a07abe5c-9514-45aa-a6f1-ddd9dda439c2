/********************************************************************************************************
 * @file	boot_32k_retn_8253_8258.link
 *
 * @brief	This is the link file for BLE SDK
 *
 * <AUTHOR> GROUP
 * @date	06,2020
 *
 * @par     Copyright (c) 2020, Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *          All rights reserved.
 *
 *          Redistribution and use in source and binary forms, with or without
 *          modification, are permitted provided that the following conditions are met:
 *
 *              1. Redistributions of source code must retain the above copyright
 *              notice, this list of conditions and the following disclaimer.
 *
 *              2. Unless for usage inside a TELINK integrated circuit, redistributions
 *              in binary form must reproduce the above copyright notice, this list of
 *              conditions and the following disclaimer in the documentation and/or other
 *              materials provided with the distribution.
 *
 *              3. Neither the name of TELINK, nor the names of its contributors may be
 *              used to endorse or promote products derived from this software without
 *              specific prior written permission.
 *
 *              4. This software, with or without modification, must only be used with a
 *              TELINK integrated circuit. All other usages are subject to written permission
 *              from TELINK and different commercial license may apply.
 *
 *              5. Licensee shall be solely responsible for any claim to the extent arising out of or
 *              relating to such deletion(s), modification(s) or alteration(s).
 *
 *          THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *          ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *          WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 *          DISCLAIMED. IN NO EVENT SHALL COPYRIGHT HOLDER BE LIABLE FOR ANY
 *          DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *          (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 *          LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 *          ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *          (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 *          SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 *******************************************************************************************************/
/* to tell the linker the program begin from __start label in cstartup.s, thus do not treat it as a unused symbol */
ENTRY(__start)

SECTIONS
{
    . = 0x0;
        .vectors :
        {
        *(.vectors)
        *(.vectors.*)	/* MUST as follows, when compile with -ffunction-sections -fdata-sections, session name may changed */
        }
        .ram_code :
        {
        *(.ram_code)
        *(.ram_code.*)
        }
        
    . = (((. + 3) / 4)*4);
        PROVIDE(_rstored_ = . );
        
        
    PROVIDE(_ramcode_size_ = . );
	PROVIDE(_ramcode_size_div_16_ = (. + 15 ) / 16);
	PROVIDE(_ramcode_size_div_256_ = (. + 255) / 256);
	PROVIDE(_ramcode_size_div_16_align_256_ = ( (. + 255) / 256) * 16);
	PROVIDE(_ramcode_size_align_256_ = ( _ramcode_size_div_16_align_256_)* 16);

    . = 0x8000; 	
        .text :
        {
        *(.text)
        *(.text.*)
        }
        .rodata :
        {
        *(.rodata)
        *(.rodata.*)
        }

    . = (((. + 3) / 4)*4);
        PROVIDE(_dstored_ = .);
        PROVIDE(_code_size_ = .);

 	. = (0x840000 + (_rstored_));
        .retention_data :
          AT ( _rstored_ )
        {
        . = (((. + 3) / 4)*4);
        PROVIDE(_retention_data_start_ = . );
        *(.retention_data)
        *(.retention_data.*)
        PROVIDE(_retention_data_end_ = . );
        }        
          

	. = 0x848900;
        .data :
          AT ( _dstored_ )
         {
    . = (((. + 3) / 4)*4);
         PROVIDE(_start_data_ = . );
         *(.data);
         *(.data.*);
    . = (((. + 3) / 4)*4);
         PROVIDE(_end_data_ = . );
         }

        .bss :
        {
	. = (((. + 3) / 4)*4);
	PROVIDE(_start_bss_ = .);
        *(.sbss)
        *(.sbss.*)
        *(.bss)
        *(.bss.*)
        }
    PROVIDE(_end_bss_ = .);

    /* data in ram but no need to clean in .s*/
        .data_no_init (NOLOAD) :
        {
    . = (((. + 3) / 4)*4);
            *(.data_no_init)
            *(.data_no_init.*)
        }
    PROVIDE(_bin_size_ = _code_size_ + _end_data_ - _start_data_);
    PROVIDE(_ictag_start_ = 0x840000 + (_ramcode_size_div_256_) * 0x100);
    PROVIDE(_ictag_end_ = 0x840000 + (_ramcode_size_div_256_ + 1) * 0x100);
    
	PROVIDE(_ram_use_size_div_16_ = (_retention_data_end_ - 0x840000  + 15 ) / 16);
    
}